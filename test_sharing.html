<!DOCTYPE html>
<html>
<head>
    <title>Test Share Links</title>
</head>
<body>
    <h1>Test Share Links</h1>
    <p>Copy a share link from the admin shareable documents page and test it here:</p>
    
    <h2>Test Cases:</h2>
    <ol>
        <li><strong>Valid Share Link:</strong> Should open the document/deviation/etc</li>
        <li><strong>Removed Share:</strong> Should show 404 after removing from admin panel</li>
        <li><strong>Invalid Share ID:</strong> Should show 404</li>
    </ol>
    
    <h2>Expected Behavior:</h2>
    <ul>
        <li>✅ Links should start with http://localhost/ (not file://)</li>
        <li>✅ Active shares should work normally</li>
        <li>❌ Removed shares should return 404</li>
        <li>❌ Invalid IDs should return 404</li>
    </ul>
    
    <h2>Test URLs:</h2>
    <p>Replace [SHARE_ID] with actual share IDs from your admin panel:</p>
    <ul>
        <li>Document: <code>http://localhost/documentview/view/[SHARE_ID]</code></li>
        <li>Deviation: <code>http://localhost/deviationview/view/[SHARE_ID]</code></li>
        <li>Event Analysis: <code>http://localhost/eventanalysisview/view/[SHARE_ID]</code></li>
        <li>Risk Assessment: <code>http://localhost/riskassessmentsview/view/[SHARE_ID]</code></li>
    </ul>
</body>
</html>
