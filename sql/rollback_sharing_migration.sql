-- Rollback Script: Revert Unified Sharing System Migration
-- Use this script if you need to rollback the migration

-- Step 1: Backup current shares table (if needed)
CREATE TABLE IF NOT EXISTS `shares_backup` AS SELECT * FROM `shares`;

-- Step 2: Restore document_shares table if it was dropped
-- (Only if you already dropped the original table)
/*
CREATE TABLE IF NOT EXISTS `document_shares` (
  `id` binary(16) NOT NULL,
  `document_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_with` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `status` enum('active','expired','revoked') NOT NULL DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_shared_by` (`shared_by`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Restore data from shares table back to document_shares
INSERT INTO `document_shares` (
    `id`, 
    `document_id`, 
    `shared_by`, 
    `shared_with`, 
    `created_at`, 
    `expires_at`, 
    `status`
)
SELECT 
    `id`,
    `entity_id` as document_id,
    `shared_by`,
    `shared_with`,
    `created_at`,
    `expires_at`,
    `status`
FROM `shares`
WHERE entity_type = 'document'
AND NOT EXISTS (
    SELECT 1 FROM `document_shares` ds 
    WHERE ds.id = shares.id
);
*/

-- Step 3: Remove shares table (if needed)
-- DROP TABLE IF EXISTS `shares`;

-- Step 4: Verification after rollback
SELECT 
    'document_shares after rollback' as table_name,
    COUNT(*) as record_count
FROM `document_shares`;

COMMIT;
