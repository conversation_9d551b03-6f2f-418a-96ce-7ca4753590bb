-- Fix duplicate shares issue
-- This script adds a unique constraint to prevent duplicate shares and removes any existing duplicates

-- Step 1: Identify and remove duplicate shares (keep the oldest one)
DELETE s1 FROM `shares` s1
INNER JOIN `shares` s2 
WHERE s1.id > s2.id 
AND s1.entity_type = s2.entity_type 
AND s1.entity_id = s2.entity_id 
AND s1.shared_by = s2.shared_by
AND s1.status = s2.status;

-- Step 2: Add unique constraint to prevent future duplicates
-- This ensures that the same entity cannot be shared multiple times by the same user
ALTER TABLE `shares` 
ADD UNIQUE KEY `unique_entity_share` (`entity_type`, `entity_id`, `shared_by`, `status`);

-- Step 3: Verify no duplicates remain
SELECT 
    entity_type,
    HEX(entity_id) as entity_id_hex,
    HEX(shared_by) as shared_by_hex,
    status,
    COUNT(*) as duplicate_count
FROM `shares`
GROUP BY entity_type, entity_id, shared_by, status
HAVING COUNT(*) > 1;

-- Step 4: Show current share statistics
SELECT 
    entity_type,
    status,
    COUNT(*) as share_count
FROM `shares`
GROUP BY entity_type, status
ORDER BY entity_type, status;

COMMIT;
