-- Verification queries for the unified sharing system migration

-- 1. Check if shares table was created successfully
DESCRIBE `shares`;

-- 2. Compare record counts between old and new tables
SELECT 
    'Original document_shares' as source,
    COUNT(*) as count
FROM `document_shares`
UNION ALL
SELECT 
    'Migrated shares (documents)' as source,
    COUNT(*) as count
FROM `shares` 
WHERE entity_type = 'document';

-- 3. Check for any missing records (should return 0)
SELECT COUNT(*) as missing_records
FROM `document_shares` ds
WHERE NOT EXISTS (
    SELECT 1 FROM `shares` s 
    WHERE s.id = ds.id 
    AND s.entity_type = 'document'
    AND s.entity_id = ds.document_id
);

-- 4. Verify data integrity - sample comparison
SELECT 
    ds.id,
    ds.document_id,
    ds.shared_by,
    ds.created_at as old_created,
    s.entity_type,
    s.entity_id,
    s.shared_by as new_shared_by,
    s.created_at as new_created
FROM `document_shares` ds
LEFT JOIN `shares` s ON s.id = ds.id
LIMIT 10;

-- 5. Check for active shares by entity type
SELECT 
    entity_type,
    COUNT(*) as active_shares
FROM `shares` 
WHERE status = 'active'
GROUP BY entity_type;

-- 6. Test share_id lookup functionality
-- Replace 'SAMPLE_SHARE_ID' with an actual share ID from your data
-- SELECT 
--     s.*,
--     HEX(s.id) as share_id_hex,
--     HEX(s.entity_id) as entity_id_hex
-- FROM `shares` s 
-- WHERE s.id = UNHEX(REPLACE('SAMPLE_SHARE_ID', '-', ''))
-- LIMIT 1;

-- 7. Performance check - ensure indexes are working
EXPLAIN SELECT * FROM `shares` 
WHERE entity_type = 'document' 
AND entity_id = UNHEX(REPLACE('sample-uuid-here', '-', ''))
AND status = 'active';
