-- Simple Migration Script: Create New Unified Shares Table Only
-- Run this in phpMyAdmin to create the new shares table without migrating old data

-- Create the new unified shares table
CREATE TABLE IF NOT EXISTS `shares` (
  `id` binary(16) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `entity_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_with` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `status` enum('active','expired','revoked') NOT NULL DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `idx_entity` (`entity_type`, `entity_id`),
  KEY `idx_shared_by` (`shared_by`),
  KEY `idx_status` (`status`),
  KEY `idx_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create additional indexes for better performance
ALTER TABLE `shares` 
ADD INDEX `idx_entity_status` (`entity_type`, `entity_id`, `status`),
ADD INDEX `idx_created_at` (`created_at`);

-- Optional: Add foreign key constraints if you have users table
-- Uncomment the lines below if you want to add foreign key relationships
-- ALTER TABLE `shares` 
-- ADD CONSTRAINT `fk_shares_shared_by` 
-- FOREIGN KEY (`shared_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Verify table creation
DESCRIBE `shares`;
