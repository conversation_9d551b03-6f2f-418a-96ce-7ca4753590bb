-- Check for duplicate shares in the database
-- Run this query to identify any duplicate entries

-- 1. Check for exact duplicates (same entity shared by same user multiple times)
SELECT 
    'Exact Duplicates' as check_type,
    entity_type,
    HEX(entity_id) as entity_id_hex,
    HEX(shared_by) as shared_by_hex,
    status,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(HEX(id) SEPARATOR ', ') as share_ids
FROM `shares`
GROUP BY entity_type, entity_id, shared_by, status
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 2. Check for potential duplicates (same entity shared multiple times, different users)
SELECT 
    'Multiple Shares per Entity' as check_type,
    entity_type,
    HEX(entity_id) as entity_id_hex,
    COUNT(*) as share_count,
    GROUP_CONCAT(DISTINCT HEX(shared_by) SEPARATOR ', ') as shared_by_users,
    GROUP_CONCAT(HEX(id) SEPARATOR ', ') as share_ids
FROM `shares`
WHERE status = 'active'
GROUP BY entity_type, entity_id
HAVING COUNT(*) > 1
ORDER BY share_count DESC;

-- 3. Show total counts by entity type and status
SELECT 
    'Summary by Type' as check_type,
    entity_type,
    status,
    COUNT(*) as total_shares
FROM `shares`
GROUP BY entity_type, status
ORDER BY entity_type, status;

-- 4. Show recent shares (last 10)
SELECT 
    'Recent Shares' as check_type,
    HEX(id) as share_id,
    entity_type,
    HEX(entity_id) as entity_id_hex,
    HEX(shared_by) as shared_by_hex,
    status,
    created_at
FROM `shares`
ORDER BY created_at DESC
LIMIT 10;
