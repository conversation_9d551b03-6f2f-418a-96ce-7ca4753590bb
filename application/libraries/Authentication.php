<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Authentication
{
	/**
	 * The CodeIgniter super object
	 *
	 * @var object
	 * @access public
	 */
	public $CI;

	/**
	 * An array holding the user ID, login time,
	 * and last modified time for a logged-in user.
	 *
	 * @var array
	 * @access private
	 */
	private $auth_identifiers = [];

	/**
	 * If the session is regenerated and the request
	 * never checks the login status, then the
	 * session will drop out on the next request.
	 */
	public $post_system_sess_check = TRUE;

	/**
	 * Use standard redirection after login.
	 * Set FALSE to do something else.
	 *
	 * @var boolean
	 */
	public $redirect_after_login = TRUE;

	// --------------------------------------------------------------

	/**
	 * Class constructor
	 */
	public function __construct()
	{
		$this->CI =& get_instance();

		// Set the auth identifiers if exists
		$this->_set_auth_identifiers();
	}

	// --------------------------------------------------------------

	/**
	 * If user is logged in they will have an auth identifier in the session,
	 * and this method will set the user ID, user's last modification time,
	 * and the user's last login time as array elements of $this->auth_identifiers.
	 */
	private function _set_auth_identifiers()
	{
		// Get the auth identifier from the session if it exists
		if( $auth_identifiers = $this->CI->session->userdata('auth_identifiers') )
		{
			$this->auth_identifiers = unserialize( $auth_identifiers );
		}
	}

	public function ldap($username)
	{
		if( empty($username) ) { return FALSE; }
		list($userdomain,$userloginid) = explode('\\',$username);

		$this->CI->config->load('ldap');

		// Construct new Adldap instance.
		$ad = new \Adldap\Adldap();

		// Create a configuration array.
		$config = [
			'hosts'    => $this->CI->config->item('ldap_hosts'),
			'base_dn'  => $this->CI->config->item('ldap_base_dn'),
			'username' => $this->CI->config->item('ldap_username'),
			'password' => $this->CI->config->item('ldap_password'),
		];

		// Add a connection provider to Adldap.
		$ad->addProvider($config);

		try {
			$ad->search()->findOrFail($userloginid);

			if( $auth_data = $this->CI->auth_model->get_auth_data_ldap( $userloginid ) )
			{
				if( $auth_data->banned === '1' )
				{
					return FALSE;
				}
				else
				{
					// Setup redirection if redirect required
					$this->redirect_after_login();

					// Set session cookie and remember me
					$this->maintain_state( $auth_data );

					// Send the auth data back to the controller
					return TRUE;
				}
			}

			return FALSE;

		} catch (\Adldap\Auth\BindException $e) {
			return FALSE;
			// if ($error = $e->getDetailedError()) {
			// 	echo $error->getErrorCode();
			// 	echo '<br/>';
			// 	echo $error->getErrorMessage();
			// 	echo '<br/>';
			// 	echo $error->getDiagnosticMessage();
			// }
		}

		return FALSE;
	}

	public function login()
	{
		// Keep post system session check from running
		$this->post_system_sess_check = FALSE;

		$this->CI->load->helper('form');
		$this->CI->load->library('form_validation');

		$validation_rules = array(
			array(
				'field' => 'login_email',
				'label' => lang('login_email'),
				'rules' => array(
					'trim',
					'valid_email'
				)
			),
			array(
				'field' => 'login_password',
				'label' => lang('login_password'),
				'rules' => array(
					'trim',
					'required',
					array('validate_auth', array( $this, '_validate_auth' ) )
				)
			)
		);

		$this->CI->form_validation->set_rules( $validation_rules );

		if( $this->CI->form_validation->run() === TRUE )
		{
			return TRUE;
		}
		return FALSE;
	}

	// --------------------------------------------------------------

	public function _validate_auth()
	{
		$user_email		= $this->CI->input->post('login_email');
		$user_password	= $this->CI->input->post('login_password');

		if( empty($user_email) OR empty($user_password) )
		{
			$this->CI->form_validation->set_message('validate_auth', lang('error_missing_fields'));
			return FALSE;
		}

		if( $auth_data = $this->CI->auth_model->get_auth_data( $user_email ) )
		{
			if( $auth_data->banned === '1' )
			{
				$this->CI->form_validation->set_message('validate_auth', lang('error_username_password'));
				return FALSE;
			}
			if( ! $this->check_password( $auth_data->passwd, $user_password ) )
			{
				$this->CI->form_validation->set_message('validate_auth', lang('error_username_password'));
			}
			else
			{
				// Setup redirection if redirect required and not an API request
				if (strpos($this->CI->uri->uri_string(), 'api/') !== 0) {
					$this->redirect_after_login();
				}

				// Set session cookie and remember me
				$this->maintain_state( $auth_data );

				// Send the auth data back to the controller
				return TRUE;
			}
		}
		else
		{
			$this->CI->form_validation->set_message('validate_auth', lang('error_username_password'));
		}

		return FALSE;
	}

	// --------------------------------------------------------------

	public function login_flex()
	{
		// Keep post system session check from running
		$this->post_system_sess_check = FALSE;

		$this->CI->load->helper('form');
		$this->CI->load->library('form_validation');

		$validation_rules = array(
			array(
				'field' => 'login_username',
				'label' => lang('login_username'),
				'rules' => array(
					'trim',
					'numeric'
				)
			),
			array(
				'field' => 'login_password',
				'label' => lang('login_password'),
				'rules' => array(
					'trim',
					'required',
					array('validate_auth', array( $this, '_validate_auth_flex' ) )
				)
			)
		);

		$this->CI->form_validation->set_rules( $validation_rules );

		if( $this->CI->form_validation->run() === TRUE )
		{
			return TRUE;
		}
		return FALSE;
	}

	// --------------------------------------------------------------

	public function _validate_auth_flex()
	{
		$login_username = $this->CI->input->post('login_username');
		$user_password  = $this->CI->input->post('login_password');

		if( empty($login_username) OR empty($user_password) )
		{
			$this->CI->form_validation->set_message('validate_auth', lang('error_missing_fields'));
			return FALSE;
		}

		if( $auth_data = $this->CI->auth_model->get_auth_data_flex( $login_username ) )
		{
			if( $auth_data->banned === '1' )
			{
				$this->CI->form_validation->set_message('validate_auth', lang('error_username_password'));
				return FALSE;
			}
			if( ! $this->check_password( $auth_data->passwd, $user_password ) )
			{
				$this->CI->form_validation->set_message('validate_auth', lang('error_username_password'));
			}
			else
			{
				// Set session cookie and remember me
				$this->maintain_state( $auth_data );

				// Setup redirection
				redirect('login/upgrade');

				// Send the auth data back to the controller
				return TRUE;
			}
		}
		else
		{
			$this->CI->form_validation->set_message('validate_auth', lang('error_username_password'));
		}

		return FALSE;
	}

	// --------------------------------------------------------------

	/**
	 * Verify if user already logged in.
	 *
	 * @param   mixed  a user level (int) or an array of user roles
	 * @return  mixed  either an object containing the user's data or FALSE
	 */
	public function check_login()
	{
		// Keep post system session check from running
		$this->post_system_sess_check = FALSE;

		// No reason to continue if auth identifiers is empty
		if( empty( $this->auth_identifiers ) )
			return FALSE;

		// Use contents of auth identifiers
		$user_id    = $this->auth_identifiers['user_id'];
		$login_time = $this->auth_identifiers['login_time'];

		/*
		 * Check database for matching user record:
		 * 1) user ID matches
		 * 2) login time matches
		 */
		$auth_data = $this->CI->auth_model->check_login_status(
			$user_id,
			$login_time
		);

		// If the query produced a match
		if( $auth_data !== FALSE )
		{
			// Confirm user
			if( $auth_data->banned === '1' )
			{
				// Logged in check failed ...
			}
			else
			{
				// If session ID was regenerated, we need to update the user record
				$this->CI->auth_model->update_user_session_id( $auth_data->user_id );

				// Send the auth data back to the controller
				return $auth_data;
			}
		}
		else
		{
			// Auth Data === FALSE because no user matching in DB ...
		}

		// Unset session
		$this->CI->session->unset_userdata('auth_identifiers');

		return FALSE;
	}

	// --------------------------------------------------------------

	/**
	 * Log the user out
	 */
	public function logout()
	{
		// Get the user ID from the session
		if( isset( $this->auth_identifiers['user_id'] ) )
		{
			/**
			 * If the session ID has been regenerated then
			 * the session ID in the auth sessions table
			 * will be the pre-regenerated session ID.
			 */
			$session_to_delete = is_null( $this->CI->session->regenerated_session_id )
				? $this->CI->session->session_id
				: $this->CI->session->pre_regenerated_session_id;

			// Delete the auth session record
			$this->CI->auth_model->logout(
				$this->auth_identifiers['user_id'],
				$session_to_delete
			);
		}

		if( config_item('delete_session_cookie_on_logout') )
		{
			// Completely delete the session cookie
			delete_cookie( config_item('sess_cookie_name') );
		}
		else
		{
			// Unset auth identifier
			$this->CI->session->unset_userdata('auth_identifiers');
		}

		$this->CI->load->helper('cookie');

		// Delete remember me cookie
		delete_cookie( config_item('remember_me_cookie_name') );

		// Garbage collection for the auth_sessions table
		if( config_item('auth_sessions_gc_on_logout') )
			$this->CI->auth_model->auth_sessions_gc();
	}

	// --------------------------------------------------------------

	/**
	 * Verify users password against the one stored in the database.
	 * @param   string  The hashed password
	 * @param   string  The raw (supplied) password
	 * @return  bool
	 */
	public function check_password( $hash, $password ) {
		// return true;
		return password_verify(
			base64_encode(
				hash('sha384', $password, true)
			),
			$hash
		);
	}

	// --------------------------------------------------------------

	/**
	 * Hash users password before storing it in the database.
	 * @param string $password
	 * @return hashed password
	 */
	public function hash_password( $password ) {
		return password_hash(
			base64_encode(
				hash('sha384', $password, true)
			),
			PASSWORD_DEFAULT
		);
	}

	// --------------------------------------------------------------

	/**
	 * Redirect after login, or not if redirect turned off
	 */
	public function redirect_after_login()
	{
		if( $this->redirect_after_login )
		{
			// Redirect to specified page, or home page if none provided
			$redirect = $this->CI->input->get(AUTH_REDIRECT_PARAM)
				? urldecode( $this->CI->input->get(AUTH_REDIRECT_PARAM) )
				: '';

			// Set the redirect protocol
			$redirect_protocol = USE_SSL ? 'https' : NULL;

			// Load URL helper for the site_url function
			$this->CI->load->helper('url');

			$url = site_url( $redirect, $redirect_protocol );

			header( "Location: " . $url, TRUE, 302 );
		}
	}

	// --------------------------------------------------------------

	/**
	 * Setup session and remember me cookie
	 * during a successful login attempt.
	 *
	 * @param   obj  the user record
	 * @return  void
	 */
	public function maintain_state( $auth_data )
	{
		// Store login time in database and cookie
		$login_time = date('Y-m-d H:i:s');

		// Check if remember me requested, and set cookie if yes
		if( config_item('allow_remember_me') && $this->CI->input->post('remember_me') )
		{
			$remember_me_cookie = [
				'name'   => config_item('remember_me_cookie_name'),
				'value'  => config_item('remember_me_expiration') + time(),
				'expire' => config_item('remember_me_expiration'),
				'domain' => config_item('cookie_domain'),
				'path'   => config_item('cookie_path'),
				'prefix' => config_item('cookie_prefix'),
				'secure' => FALSE
			];

			$this->CI->input->set_cookie( $remember_me_cookie );

			// Make sure the CI session cookie doesn't expire on close
			$this->CI->session->sess_expire_on_close = FALSE;
			$this->CI->session->sess_expiration = config_item('remember_me_expiration');
		}

		// Create the auth identifier
		$auth_identifiers = serialize([
			'user_id'    => $auth_data->user_id,
			'login_time' => $login_time
		]);

		// Set CI session cookie
		$this->CI->session->set_userdata( 'auth_identifiers', $auth_identifiers );

		// For security, force regenerate the session ID
		$session_id = $this->CI->session->sess_regenerate( config_item('sess_regenerate_destroy') );

		// Update user record in database
		$this->CI->auth_model->login_update(
			$auth_data->user_id,
			$login_time,
			$session_id
		);
	}

	// -----------------------------------------------------------------------
}
