<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Eventanalysisview extends MY_Controller
{

  public function __construct()
	{
		parent::__construct();
		$this->load->model(array('deviation_model'));
    $this->load->model(array('group_model'));
		$this->load->model(array('user_model'));
    $db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('eventanalysislib', $db);
    $this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
    $this->auth_company_id  = $this->deviation_model->get_company_id();
		$this->users = $this->user_model->get_all();
		$CI =& get_instance();
		$CI->users_all = $this->users;
  }
  public function view( $ea_id )
	{
		// Check if this is a share_id instead of event analysis id
		if( $this->VALID_UUIDv4($ea_id) )
		{
			$this->load->model('share_model');
			$share = $this->share_model->get_share_by_id($ea_id);
			
			if( $share && $share->entity_type === 'eventanalysis' && $share->status === 'active' )
			{
				// This is a valid active share, use the actual event analysis id
				$ea_id = $share->entity_id;
			}
			else
			{
				// Either no share found, wrong entity type, or inactive share
				// Try to check if it's a direct event analysis id (for backward compatibility)
				$test_ea = $this->eventanalysislib->getEventAnalysis($ea_id);
				if( empty($test_ea) ) { 
					show_404(); 
				}
				// If it's a direct EA access without share, still allow it for now
			}
		}
		else
		{
			show_404();
		}

		$getEventAnalysis = $this->eventanalysislib->getEventAnalysis($ea_id);
		if( empty($getEventAnalysis))
			show_404();
		list($getEventAnalysisQuestions,$getEventAnalysisQuestionsAnswers) = $this->eventanalysislib->getEventAnalysisQuestions($ea_id);
		$getEventAnalysisActionList = $this->eventanalysislib->getEventAnalysisActionList($ea_id);
		
		$getEventAnalysisDeviationMap       = $this->eventanalysislib->getEventAnalysisDeviationMap($ea_id);
		$getEventAnalysisRiskAssessmentsMap = $this->eventanalysislib->getEventAnalysisRiskAssessmentsMap($ea_id);
		$departments = $this->deviation_model->deviationDepartment();
		$getDeviationNames                  = $this->eventanalysislib->getDeviationNames($departments,$getEventAnalysisDeviationMap,$this->auth_company_id);
		$getRiskAnalysisNames               = $this->eventanalysislib->getRiskAnalysisNames($this->auth_company_id);
		
		$this->data['getEventAnalysis']                   = $getEventAnalysis;
		$this->data['getEventAnalysisQuestions']          = $getEventAnalysisQuestions;
		$this->data['getEventAnalysisQuestionsAnswers']   = $getEventAnalysisQuestionsAnswers;
		$this->data['getEventAnalysisActionList']         = $getEventAnalysisActionList;
		$this->data['getEventAnalysisDeviationMap']       = $getEventAnalysisDeviationMap;
		$this->data['getDeviationNames']                  = $getDeviationNames;
		$this->data['getEventAnalysisRiskAssessmentsMap'] = $getEventAnalysisRiskAssessmentsMap;
		$this->data['getRiskAnalysisNames']               = $getRiskAnalysisNames;
		
		$this->data['fields'] = array(
			array(
				'id' => 'name',
				'input' => 'input',
				'required' => 1,
				'title' => 'Rubrik',
				'description' => 'Ange kortfattat vad som rapporteras.',
				'value' => $getEventAnalysis->name,
				'values' => null,
			),
			array(
				'id' => 'redo',
				'input' => 'text',
				'required' => 1,
				'title' => 'Redogörelse',
				'description' => 'Fördjupad redogörelse.',
				'value' => $getEventAnalysis->redo,
				'values' => null,
			)
		);
	
		$this->data['fields'] = array_merge($this->data['fields'], array(
			array(
				'id' => 'us_id',
				'input' => 'users',
				'required' => 0,
				'title' => 'Användare',
				'description' => 'Vem är ansvarig för händelseanalysen?',
				'value' => $getEventAnalysis->us_id,
				'values' => $this->users
			),
		));
		
		$this->data['fieldsDone'] = array(
			array(
				'id' => 'plan',
				'input' => 'text',
				'required' => 1,
				'title' => 'Handlingsplan',
				'description' => 'Hur skall ni se till att händelsen inte upprepas?',
				'value' => $getEventAnalysis->plan
			),
			array(
				'id' => 'done',
				'input' => 'date',
				'required' => 0,
				'title' => 'Datum',
				'description' => 'Händelseanalysen är färdig.',
				'value' => $getEventAnalysis->done
			)
		);
		
		$this->data['eventFields'] = array(
			'question' => array(
				'type' => 'input',
				'name' => 'eventQuestion[]',
				'class' => 'required form-control eventQuestion',
				'placeholder' => 'Fråga',
				'title' => 'Fråga',
			),
			'answer' => array(
				'type' => 'input',
				'name' => 'eventAnswer[]',
				'class' => 'form-control eventAnswer',
				'placeholder' => 'Svar',
				'title' => 'Svar',
			),
		);
		
		$this->data['eventFields'] = array_merge($this->data['eventFields'], array(
			'responsible' => array(
				'type' => 'users',
				'name' => 'eventResponsible[]',
				'class' => 'required form-control eventResponsible',
				'values' => $this->users,
				'title' => 'Vem skall besvara frågan?',
			),
		));
		
		$this->data['actionListFields'] = array(
			'name' => array(
				'type' => 'input',
				'name' => 'eventActionListName[]',
				'class' => 'required',
				'title' => 'Åtgärd',
				'placeholder' => 'Åtgärd'
			),
		);
		
		$this->data['actionListFields'] = array_merge($this->data['actionListFields'], array(
			'responsible' => array(
				'type' => 'users',
				'name' => 'eventActionListResponsible[]',
				'class' => 'required',
				'values' => $this->users,
				'title' => 'Vem är ansvarig?',
			),
		));
		
		$this->data['actionListFields'] = array_merge($this->data['actionListFields'], array(
			'description' => array(
				'type' => 'textarea',
				'name' => 'eventActionListDescription[]',
				'title' => 'Beskrivning',
				'placeholder' => 'Förklarande text...'
			),
			'done' => array(
				'title' => 'Är åtgärden färdig?',
				'type' => 'checkbox',
				'name' => 'eventActionListDone[]',
				'values' => 1,
				'title' => 'Är åtgärden färdig?',
			)
		));

		$this->load->view('general/eventanalysis/viewpublic',$this->data);
	}

}
