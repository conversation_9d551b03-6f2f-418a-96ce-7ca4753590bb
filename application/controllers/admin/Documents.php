<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Documents extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model(['document_model','folder_model']);
	}
	
    public function index()
	{
		$this->view();
	}

	public function get_kvo()
	{
		// The URL of the external endpoint returning the .docx file
		$externalUrl = 'https://orna-analys.kvalprak.se/admin/getDocumentOrna?companyName='.$this->config->item('program_name').'&type=kvo';
		// $externalUrl = 'http://localhost:3000/admin/getDocumentOrna?companyName=Skaraborgshalsan&type=kvo';

		$ch = curl_init($externalUrl);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			'X-API-KEY: !0PKMt9b95?lY&i7Hhx`Z{k1pnr@g',
		]);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		// Execute the cURL request
		$response = curl_exec($ch);

		// Close cURL
		curl_close($ch);

		// Set headers for downloading the file
		header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
		header('Content-Disposition: attachment; filename="report.docx"');
		echo $response;
	}

	public function get_report()
	{
		// The URL of the external endpoint returning the .docx file
		$externalUrl = 'https://orna-analys.kvalprak.se/admin/getReportOrna?companyName='.$this->config->item('program_name').'&type=kvo';
		// $externalUrl = 'http://localhost:3000/admin/getReportOrna?companyName=Skaraborgshalsan&type=kvo';


		$ch = curl_init($externalUrl);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			'X-API-KEY: !0PKMt9b95?lY&i7Hhx`Z{k1pnr@g',
		]);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		// Execute the cURL request
		$response = curl_exec($ch);

		// Close cURL
		curl_close($ch);

		// Set headers for downloading the file
		header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
		header('Content-Disposition: attachment; filename="report.docx"');
		echo $response;
	}

	public function orna_analys_auto() {
		$this->load->helper('form');
		$this->data['success'] = false;
		$this->data["limit"] = false;
		if( $this->input->method(TRUE) === 'POST' )
		{
			$this->load->model('document_model');
			$this->document_model->send_to_orna_analys('', $this->config->item('program_name'), $this->acl['deviation']['read']);
			$this->data['success'] = true;
		}
		$this->load->view('admin/documents/orna_analys_auto', $this->data);
	}

	public function orna_analys()
	{
		$this->load->helper('form');
		$this->data['success'] = false;
		$this->data["limit"] = false;
		if( $this->input->method(TRUE) === 'POST' )
		{
			$this->load->model('document_model');
			$orna_analysis_path = CI_UPLOAD_PATH . 'orna-analys.json';
			$array = [$this->input->post('email')];
			if(file_exists($orna_analysis_path))
			{
				if(($json = file_get_contents($orna_analysis_path)) !== FALSE)
				{
					if(($array = json_decode($json, true)) !== NULL) {
						if (!in_array($this->input->post('email'), $array)) 
						{
							if (count($array) >= 4 ){
								$this->data["limit"] = true;
								$this->load->view('admin/documents/orna_analys', $this->data);
								return;
							}
							$array[] = $this->input->post('email');
						}
					}
				}
			}
			if(($json = json_encode($array)) !== FALSE)
			{
				file_put_contents($orna_analysis_path, $json);
			}
			
			$this->document_model->send_to_orna_analys($this->input->post('email'), $this->config->item('program_name'), $this->acl['deviation']['read']);
			$this->data['success'] = true;
		} 
		$this->load->view('admin/documents/orna_analys', $this->data);
	}
	
	public function view()
	{
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		$this->load->view('admin/documents/view',$this->data);
	}
	
	public function categories($type = 'display', $id = NULL)
	{
		in_array($type,[
			'display',
			'create',
			'update',
			'delete',
		],TRUE) OR show_404();

		$this->VALID_UUIDv4($id, FALSE);

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$callback = 'categories_' . $type;
		$this->{$callback}($id);
	}
	
	public function categories_display($id)
	{
		$this->data['categories'] = $this->document_model->get_document_category();
		$this->data['categories_in_use'] = $this->document_model->get_document_category_in_use();
		$this->load->view('admin/documents/display_categories',$this->data);
	}
	
	public function categories_create($id)
	{
		$validation_rules = $this->_get_rules_categories();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->document_model->create_categories() === TRUE )
			{
				redirect('admin/documents/categories'); exit;
			}
			
			// @STEP2: Give error
			redirect('admin/documents/categories'); exit;
		}
		else
		{
			$this->load->view('admin/documents/create_categories',$this->data);
		}
	}
	
	public function categories_update($id)
	{
		$this->data['category'] = $this->document_model->get_document_category($id);
		if( empty($this->data['category']) ) { show_404(); }
		
		$validation_rules = $this->_get_rules_categories();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->document_model->update_categories($id) === TRUE )
			{
				redirect('admin/documents/categories'); exit;
			}
			
			// @STEP2: Give error
			redirect('admin/documents/categories'); exit;
		}
		else
		{
			$this->load->view('admin/documents/update_categories',$this->data);
		}
	}
	
	public function categories_delete($id)
	{
		$this->data['category'] = $this->document_model->get_document_category($id);
		if( empty($this->data['category']) ) { show_404(); }
		
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->document_model->delete_categories( $id ) === TRUE )
			{
				redirect('admin/documents/categories'); exit;
			}
			
			// @STEP2: Give error
			redirect('admin/documents/categories'); exit;
		}
		else
		{
			$this->load->view('admin/documents/delete_categories',$this->data);
		}
	}
	
	public function owner()
	{
		$owners = $this->document_model->get_all_by_owner(array_keys($this->users_all));
		if( ! empty($owners) )
			$owners = array_keys($owners);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_owner();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$this->document_model->change_owner();
			redirect('admin/documents/owner'); exit;
		}
		else
		{
			$from = [];
			$to   = [];
			if( ! empty($owners) )
			{
				foreach($this->users_all as $user_id => $user)
				{
					if( in_array($user_id,$owners) )
					{
						$from[$user_id] = $this->users_all[$user_id]->name;
					}
				}
				foreach($this->users as $user_id => $user)
				{
					$to[$user_id] = $this->users[$user_id]->name;
				}
			}
			$this->data['from'] = $from;
			$this->data['to'] = $to;
			// var_dump($this->users,$from,$to);
			$this->load->view('admin/documents/owner',$this->data);
		}
	}
	
	public function owner_individual()
	{
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_owner_individual();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->document_model->change_owner_individual() )
			{
				
			}
			
			redirect('admin/documents/owner_individual'); exit;
		}
		else
		{
			foreach($this->users as $user_id => $user)
			{
				$to[$user_id] = $this->users[$user_id]->name;
			}
			$this->data['to'] = $to;
			$this->load->view('admin/documents/owner_individual',$this->data);
		}
	}
	
	public function move()
	{
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_move();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->input->post('copy_only'))
				$this->document_model->copy_multiple();
			else 
				$this->document_model->move_multiple();
			
			redirect('admin/documents/move'); exit;
		}
		else
		{
			$this->load->view('admin/documents/move',$this->data);
		}
	}
	
	private function _get_rules_categories()
	{
		return array(
			array(
				'field' => 'documents_categories_name',
				'label' => lang('documents_categories_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[100]', // 255
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);
	}
	
	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_owner()
	{
		return array(
			array(
				'field' => 'documents_owner_from',
				'label' => lang('documents_owner'),
				'rules' => array(
					'required',
					'in_list['.implode(',',array_keys($this->users_all)).']'
				)
			),
			array(
				'field' => 'documents_owner_to',
				'label' => lang('documents_owner'),
				'rules' => array(
					'required',
					'in_list['.implode(',',array_keys($this->users)).']'
				)
			),
			array(
				'field' => 'confirm_change',
				'label' => lang('confirm_change'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_owner_individual()
	{
		return array(
			array(
				'field' => 'documents_owner_to',
				'label' => lang('documents_owner'),
				'rules' => array(
					'required',
					'in_list['.implode(',',array_keys($this->users)).']'
				)
			),
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',$this->data['move']['document']['version']).']'
				)
			),
			array(
				'field' => 'confirm_change',
				'label' => lang('confirm_change'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_move()
	{
		return array(
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',$this->data['move']['document']['version']).']'
				)
			),
			array(
				'field' => 'move_document',
				'label' => lang('folder_folder'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['move']['folder']['id'])).']'
				),
			),
			array(
				'field' => 'confirm_change',
				'label' => lang('confirm_change'),
				'rules' => array(
					'required'
				)
			),
		);
	}

	/**
	 * Shareable Documents - Admin view
	 * Display all shared entities with management options
	 */
	public function shareable()
	{
		$this->load->model('share_model');

		// Get all shared entities (documents, deviations, event analysis, risk assessments)
		$shared_entities = $this->share_model->get_all_shared_entities();
		
		// Initialize entities_by_type array
		$entities_by_type = array();

		// Enrich entities with their names and details
		if (!empty($shared_entities)) {
			// Group entities by type for batch processing
			foreach ($shared_entities as $entity) {
				if (!isset($entities_by_type[$entity->entity_type])) {
					$entities_by_type[$entity->entity_type] = array();
				}
				$entities_by_type[$entity->entity_type][] = $entity;
			}

			// Process each entity type in batches to reduce database queries
			foreach ($entities_by_type as $entity_type => $entities) {
				switch ($entity_type) {
					case 'document':
						// Get all document IDs for batch query
						$document_ids = array();
						foreach ($entities as $entity) {
							$document_ids[] = UUID_TO_BIN($entity->entity_id);
						}

						if (!empty($document_ids)) {
							// Batch query for documents
							$this->db->select('documents.document_id, documents.name, documents.description, folders.name as folder_name, menus.name as menu_name');
							$this->db->from('documents');
							$this->db->join('folders', 'folders.folder_id = documents.folder_id', 'left');
							$this->db->join('menus', 'menus.menu_id = folders.menu_id', 'left');
							$this->db->where_in('documents.document_id', $document_ids);
							$doc_query = $this->db->get();

							$documents_data = array();
							if ($doc_query->num_rows() > 0) {
								foreach ($doc_query->result() as $doc) {
									$doc_id = BIN_TO_UUID($doc->document_id);
									$documents_data[$doc_id] = $doc;
								}
							}

							// Batch query for file extensions
							$this->db->select('document_id, file_ext');
							$this->db->from('document_attachment');
							$this->db->where_in('document_id', $document_ids);
							$ext_query = $this->db->get();

							$extensions_data = array();
							if ($ext_query->num_rows() > 0) {
								foreach ($ext_query->result() as $ext) {
									$doc_id = BIN_TO_UUID($ext->document_id);
									if (!isset($extensions_data[$doc_id])) {
										$extensions_data[$doc_id] = $ext->file_ext;
									}
								}
							}

							// Apply data to entities
							foreach ($entities as &$entity) {
								$entity->entity_name = '';
								$entity->entity_description = '';
								$entity->folder_name = '';
								$entity->menu_name = '';
								$entity->file_ext = '';

								if (isset($documents_data[$entity->entity_id])) {
									$doc = $documents_data[$entity->entity_id];
									$entity->entity_name = $doc->name;
									$entity->entity_description = $doc->description;
									$entity->folder_name = $doc->folder_name;
									$entity->menu_name = $doc->menu_name;
								}

								if (isset($extensions_data[$entity->entity_id])) {
									$entity->file_ext = $extensions_data[$entity->entity_id];
								}
							}
						}
						break;
						
					case 'deviation':
						// Get all deviation IDs for batch query
						$deviation_ids = array();
						foreach ($entities as $entity) {
							$deviation_ids[] = UUID_TO_BIN($entity->entity_id);
						}

						if (!empty($deviation_ids)) {
							// Batch query for deviations
							$this->db->select('
								da.a_id,
								MAX(CASE WHEN da.df_id = 0x9edbc3cc22934bcf90048e1fdd0e5252 THEN da.answer END) as name,
								MAX(CASE WHEN da.df_id = 0xc3da451e6168492d84ceee24b8aebda7 THEN da.answer END) as description
							');
							$this->db->from('deviation_answers da');
							$this->db->where_in('da.a_id', $deviation_ids);
							$this->db->where_in('da.df_id', [
								UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252'), // Rubrik
								UUID_TO_BIN('c3da451e-6168-492d-84ce-ee24b8aebda7')  // Ärendebeskrivning
							]);
							$this->db->group_by('da.a_id');
							$dev_query = $this->db->get();

							$deviations_data = array();
							if ($dev_query->num_rows() > 0) {
								foreach ($dev_query->result() as $dev) {
									$dev_id = BIN_TO_UUID($dev->a_id);
									$deviations_data[$dev_id] = $dev;
								}
							}

							// Apply data to entities
							foreach ($entities as &$entity) {
								$entity->entity_name = '';
								$entity->entity_description = '';
								$entity->folder_name = '';
								$entity->menu_name = '';
								$entity->file_ext = '';

								if (isset($deviations_data[$entity->entity_id])) {
									$dev = $deviations_data[$entity->entity_id];
									$entity->entity_name = $dev->name ?: 'Ingen rubrik';
									$entity->entity_description = $dev->description ?: '';
								}
							}
						}
						break;
						
					case 'eventanalysis':
						// Get all event analysis IDs for batch query
						$ea_ids = array();
						foreach ($entities as $entity) {
							$ea_ids[] = UUID_TO_BIN($entity->entity_id);
						}

						if (!empty($ea_ids)) {
							$this->db->select('id, name, redo as description');
							$this->db->from('eventanalysis');
							$this->db->where_in('id', $ea_ids);
							$ea_query = $this->db->get();

							$ea_data = array();
							if ($ea_query->num_rows() > 0) {
								foreach ($ea_query->result() as $ea) {
									$ea_id = BIN_TO_UUID($ea->id);
									$ea_data[$ea_id] = $ea;
								}
							}

							// Apply data to entities
							foreach ($entities as &$entity) {
								$entity->entity_name = '';
								$entity->entity_description = '';
								$entity->folder_name = '';
								$entity->menu_name = '';
								$entity->file_ext = '';

								if (isset($ea_data[$entity->entity_id])) {
									$ea = $ea_data[$entity->entity_id];
									$entity->entity_name = $ea->name;
									$entity->entity_description = $ea->description;
								}
							}
						}
						break;

					case 'riskassessment':
						// Get all risk assessment IDs for batch query
						$ra_ids = array();
						foreach ($entities as $entity) {
							$ra_ids[] = UUID_TO_BIN($entity->entity_id);
						}

						if (!empty($ra_ids)) {
							$this->db->select('ra_id, name, description');
							$this->db->from('risk_assessments');
							$this->db->where_in('ra_id', $ra_ids);
							$ra_query = $this->db->get();

							$ra_data = array();
							if ($ra_query->num_rows() > 0) {
								foreach ($ra_query->result() as $ra) {
									$ra_id = BIN_TO_UUID($ra->ra_id);
									$ra_data[$ra_id] = $ra;
								}
							}

							// Apply data to entities
							foreach ($entities as &$entity) {
								$entity->entity_name = '';
								$entity->entity_description = '';
								$entity->folder_name = '';
								$entity->menu_name = '';
								$entity->file_ext = '';

								if (isset($ra_data[$entity->entity_id])) {
									$ra = $ra_data[$entity->entity_id];
									$entity->entity_name = $ra->name;
									$entity->entity_description = $ra->description;
								}
							}
						}
						break;
				}
			}
		} else {
			// No shared entities found, initialize empty array
			log_message('debug', 'No shared entities found');
		}

		// Flatten the entities back to a single array and ensure uniqueness
		$final_entities = array();
		$seen_share_ids = array();

		if (!empty($entities_by_type)) {
			foreach ($entities_by_type as $entity_type => $entities) {
				foreach ($entities as $entity) {
					if (!in_array($entity->share_id, $seen_share_ids)) {
						$final_entities[] = $entity;
						$seen_share_ids[] = $entity->share_id;
					}
				}
			}
		}

		$this->data['shared_documents'] = $final_entities;

		// Debug: Log the actual data
		log_message('debug', 'Total shared entities found: ' . count($this->data['shared_documents']));
		foreach ($this->data['shared_documents'] as $index => $entity) {
			log_message('debug', "Entity $index: ID={$entity->entity_id}, Type={$entity->entity_type}, Name=" . ($entity->entity_name ?? 'N/A'));
		}
		
		// Calculate statistics
		$total_shared = count($this->data['shared_documents']);
		$this_month = 0;
		$this_week = 0;
		$by_type = [];
		
		$current_month = date('Y-m');
		$current_week_start = date('Y-m-d', strtotime('monday this week'));
		
		foreach ($this->data['shared_documents'] as $entity) {
			// Count by type
			if (!isset($by_type[$entity->entity_type])) {
				$by_type[$entity->entity_type] = 0;
			}
			$by_type[$entity->entity_type]++;
			
			// Count this month - use shared_date instead of created_at
			if (isset($entity->shared_date) && date('Y-m', strtotime($entity->shared_date)) == $current_month) {
				$this_month++;
			}
			
			// Count this week - use shared_date instead of created_at  
			if (isset($entity->shared_date) && date('Y-m-d', strtotime($entity->shared_date)) >= $current_week_start) {
				$this_week++;
			}
		}

		$this->data['stats'] = [
			'total_shared' => $total_shared,
			'this_month' => $this_month,
			'this_week' => $this_week,
			'by_type' => $by_type
		];

		$this->load->view('admin/documents/shareable', $this->data);
	}

	/**
	 * Remove document share via AJAX
	 */
	public function remove_share()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
		{
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Invalid request'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$share_id = $this->input->post('share_id');
		$document_id = $this->input->post('document_id'); // For backward compatibility
		
		if( empty($share_id) && empty($document_id) )
		{
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('shareable_remove_failed')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$id_to_validate = !empty($share_id) ? $share_id : $document_id;

		try {
			$this->VALID_UUIDv4($id_to_validate);
		} catch (Exception $e) {
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('shareable_remove_failed')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$this->load->model('share_model');

		$success = false;
		if( !empty($share_id) )
		{
			// Remove by share_id (preferred method)
			$success = $this->share_model->remove_share_by_id($share_id);
		}
		elseif( !empty($document_id) )
		{
			// Remove by document_id (backward compatibility)
			$success = $this->share_model->remove_share('document', $document_id);
		}

		if( $success )
		{
			$this->output
					->set_status_header(200)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => TRUE, 'message' => lang('shareable_share_removed')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
		}
		else
		{
			$this->output
					->set_status_header(500)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('shareable_remove_failed')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
		}
		exit;
	}
}
