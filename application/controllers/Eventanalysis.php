<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Eventanalysis extends User_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('eventanalysislib', $db);
		$this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
		$this->departments = $this->groups['types']['uuid']['department'];
		$this->data['sidebar']['module'] = 'eventanalysis';
		// $this->data['body_class'] .= " sidebar-collapse";
	}
	
	public function add($a_id = NULL)
	{
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['create']) ) { show_404(); }
		
		$this->VALID_UUIDv4($a_id,FALSE);
		
		$EventDeviation = FALSE;
		
		if( $a_id !== NULL)
		{
			$getEventAnalysisDeviationUserById = $this->eventanalysislib->getEventAnalysisDeviationUserById($a_id,$this->auth_company_id);
			if( empty($getEventAnalysisDeviationUserById) ) { show_404(); }
			$EventDeviation = TRUE;
		}

		$this->data['EventDeviation'] = $EventDeviation;
		
		if( $this->input->method(TRUE) === 'POST' )
		{
			// All general forms
			$answers = array();
			$userMessages = array();
			foreach($_POST as $key => $val) {
				if( $key != 'submit' && 
					!preg_match('/event/',$key)
				) {
					$answers[0][$key] = $val;
				}
			}
			
			// Händelseanalys spara
			$ea_id = $this->eventanalysislib->insertEventAnalysis($answers,$this->auth_company_id);
			
			// Grundfråga
			if(isset($_POST['eventId'])) {
				$events = array();
				$events_id = array();
				$i = 0;
				foreach($_POST['eventId'] as $id => $event) {
					$question_id = UUIDv4();
					$events_id[] = $question_id;
					$events[] = array(
						'id'			=> UUID_TO_BIN($question_id),
						'ea_id'			=> UUID_TO_BIN($ea_id),
						'parent'		=> NULL,
						'question'		=> $_POST['eventQuestion'][$i],
						'answer'		=> $_POST['eventAnswer'][$i],
						'responsible'	=> isset($_POST['eventResponsible'][$i]) ? UUID_TO_BIN($_POST['eventResponsible'][$i]) : null
					);
					if (
						empty($_POST['eventAnswer'][$i]) && 
						!empty($_POST['eventResponsible'][$i]) && 
						!in_array($_POST['eventResponsible'][$i],$userMessages)
					) {
						$userMessages[] = $_POST['eventResponsible'][$i];
					}

					++$i;
				}
			}
			
			// Grunfråga spara
			if(!empty($events))
				$questionIds = $this->eventanalysislib->insertEventAnalysisQuestions($events);
			
			// Följdfråga
			if(isset($_POST['eventParent']) && $questionIds) {
				$eventsChild = array();
				$eventIdLast = $_POST['eventParent'][0];
				$i = 0;
				$qi = 0;
				foreach($_POST['eventIdChild'] as $id => $event) {
					if($eventIdLast != $_POST['eventParent'][$i])
						++$qi;
					$eventIdLast = $_POST['eventParent'][$i];
					$question_id = UUIDv4();
					$eventsChild[] = array(
						'ID'			=> UUID_TO_BIN($question_id),
						'ea_id'			=> UUID_TO_BIN($ea_id),
						'parent'		=> UUID_TO_BIN($events_id[$qi]),
						'question'		=> $_POST['eventQuestionChild'][$i],
						'answer'		=> $_POST['eventAnswerChild'][$i],
						'responsible'	=> isset($_POST['eventResponsibleChild'][$i]) ? UUID_TO_BIN($_POST['eventResponsibleChild'][$i]) : null
					);
					if (
						empty($_POST['eventAnswerChild'][$i]) &&
						!empty($_POST['eventResponsibleChild'][$i]) && 
						!in_array($_POST['eventResponsibleChild'][$i],$userMessages)
					) {
						$userMessages[] = $_POST['eventResponsibleChild'][$i];
					}
						
					++$i;
				}
			}
			
			// Följdfråga spara
			if(!empty($eventsChild))
				$this->eventanalysislib->insertEventAnalysisQuestions($eventsChild);
			
			$this->load->model('user_messages_model');
			
			// Spara anslutningen till avvikelse
			if($EventDeviation) {
				// Generera data array
				$deviation = array();
				$deviation[] = array(
					'a_id'			=> UUID_TO_BIN($a_id),
					'ea_id'			=> UUID_TO_BIN($ea_id),
				);
				
				// Spara och ta bort meddelanden, om inte har fler uppgifter.
				if(!empty($deviation)) {
					$deviationSaved = $this->eventanalysislib->insertEventAnalysisDeviationMap($deviation,$ea_id);
					if($deviationSaved) {
						$deviationUpdated     = $this->eventanalysislib->updateEventAnalysisDeviation(array('eventanalysis' => NULL),$a_id);
						$eventanalysisMessage = $this->user_messages_model->remove('eventanalysis', $this->auth_user_id, $a_id);
						if($deviationUpdated && $eventanalysisMessage) {
							// Just in case there are any eventanalysis left, delete all messages
							$getEventAnalysisDeviation = $this->eventanalysislib->getEventAnalysisDeviation($this->departments,$this->auth_user_id,$this->auth_company_id);
							if(empty($getEventAnalysisDeviation)) {
								$this->user_messages_model->remove_all_actions( 'eventanalysis', 'create', $this->auth_user_id );
							}
						}
					}
				}
			}
			
			// @STEP2: create_multiple and save them
			if(!empty($userMessages)) {
				foreach($userMessages AS $user) {
					$this->user_messages_model->create(
						$user,
						'eventanalysis',
						$ea_id,
						'qa',
						'warning'
					);
				}
			} elseif (! empty($this->data['rights']['update'])) {
				redirect('eventanalysis/edit/' . $ea_id); exit;
			}

			redirect('eventanalysis/view/' . $ea_id); exit;
		}
		else
		{
			$this->data['fields'] = array(
				array(
					'id' => 'name',
					'input' => 'input',
					'required' => 1,
					'title' => 'Rubrik',
					'description' => 'Ange kortfattat vad som rapporteras.',
					'value' => null,
					'values' => null,
				),
				array(
					'id' => 'redo',
					'input' => 'text',
					'required' => 1,
					'title' => 'Redogörelse',
					'description' => 'Fördjupad redogörelse.',
					'value' => null,
					'values' => null,
				)
			);
			
			if( $this->auth_kiv )
			{
				$this->data['fields'] = array_merge($this->data['fields'], array(
					array(
						'id' => 'us_id',
						'input' => 'users',
						'required' => 0,
						'title' => 'Användare',
						'description' => 'Vem är ansvarig för händelseanalysen?',
						'value' => $EventDeviation ? $getEventAnalysisDeviationUserById->eventanalysis : NULL,
						'values' => $this->users
					),
				));
			}
			
			$this->data['eventFields'] = array(
				'question' => array(
					'type' => 'input',
					'name' => 'eventQuestion[]',
					'class' => 'required form-control eventQuestion',
					'placeholder' => 'Fråga',
					'title' => 'Fråga',
				),
				'answer' => array(
					'type' => 'input',
					'name' => 'eventAnswer[]',
					'class' => 'form-control eventAnswer',
					'placeholder' => 'Svar',
					'title' => 'Svar',
				),
			);
			
			if( $this->auth_kiv )
			{
				$this->data['eventFields'] = array_merge($this->data['eventFields'], array(
					'responsible' => array(
						'type' => 'users',
						'name' => 'eventResponsible[]',
						'class' => 'required form-control eventResponsible',
						'values' => $this->users,
						'title' => 'Vem skall besvara frågan?',
					),
				));
			}
			
			$this->load->view('general/eventanalysis/create',$this->data);
		}
	}
	
	public function edit( $ea_id = NULL)
	{
		$this->VALID_UUIDv4($ea_id);
		$getEventAnalysis = $this->eventanalysislib->getEventAnalysis($ea_id);
		if( empty($getEventAnalysis) OR $getEventAnalysis->company_id !== $this->auth_company_id )
			show_404();

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) && $getEventAnalysis->us_id !== $this->auth_user_id ) { show_404(); }

		list($getEventAnalysisQuestions,$getEventAnalysisQuestionsAnswers) = $this->eventanalysislib->getEventAnalysisQuestions($ea_id);
		$getEventAnalysisActionList = $this->eventanalysislib->getEventAnalysisActionList($ea_id);
		
		$this->data['getEventAnalysis']                 = $getEventAnalysis;
		$this->data['getEventAnalysisQuestions']        = $getEventAnalysisQuestions;
		$this->data['getEventAnalysisQuestionsAnswers'] = $getEventAnalysisQuestionsAnswers;
		$this->data['getEventAnalysisActionList']       = $getEventAnalysisActionList;

		// var_dump($this->users[$getEventAnalysis->us_id]);
		// var_dump($this->users);
		// var_dump($getEventAnalysis);exit;
		
		$EventDeviation = false;
		$this->data['EventDeviation'] = $EventDeviation;

		if( $this->input->method(TRUE) === 'POST' )
		{
			// All general forms
			$answers = array();
			$userMessages = array();
			foreach($_POST as $key => $val) {
				if( $key != 'submit' && 
					!preg_match('/event/',$key)
				) {
					$answers[0][$key] = $val;
				}
			}
			
			// Grundfråga
			if(isset($_POST['eventId'])) {
				$events = array();
				$events_id = array();
				$i = 0;
				foreach($_POST['eventId'] as $id => $event) {
					$question_id = UUIDv4();
					$events_id[] = $question_id;
					$events[] = array(
						'id'			=> UUID_TO_BIN($question_id),
						'ea_id'			=> UUID_TO_BIN($ea_id),
						'parent'		=> NULL,
						'question'		=> $_POST['eventQuestion'][$i],
						'answer'		=> $_POST['eventAnswer'][$i],
						'responsible'	=> isset($_POST['eventResponsible'][$i]) ? UUID_TO_BIN($_POST['eventResponsible'][$i]) : null
					);

					if (
						empty($_POST['eventAnswer'][$i]) && 
						!empty($_POST['eventResponsible'][$i]) && 
						!in_array($_POST['eventResponsible'][$i],$userMessages)
					) {
						$userMessages[] = $_POST['eventResponsible'][$i];
					}

					++$i;
				}
			}
			
			// Grunfråga spara
			if(!empty($events))
				$questionIds = $this->eventanalysislib->insertEventAnalysisQuestions($events);
			
			// Följdfråga
			if(isset($_POST['eventParent']) && $questionIds) {
				$eventsChild = array();
				$eventIdLast = $_POST['eventParent'][0];
				$i = 0;
				$qi = 0;
				foreach($_POST['eventIdChild'] as $id => $event) {
					if($eventIdLast != $_POST['eventParent'][$i])
						++$qi;
					$eventIdLast = $_POST['eventParent'][$i];
					$question_id = UUIDv4();
					$eventsChild[] = array(
						'id'			=> UUID_TO_BIN($question_id),
						'ea_id'			=> UUID_TO_BIN($ea_id),
						'parent'		=> UUID_TO_BIN($events_id[$qi]),
						'question'		=> $_POST['eventQuestionChild'][$i],
						'answer'		=> $_POST['eventAnswerChild'][$i],
						'responsible'	=> isset($_POST['eventResponsibleChild'][$i]) ? UUID_TO_BIN($_POST['eventResponsibleChild'][$i]) : null
					);
					if (
						empty($_POST['eventAnswerChild'][$i]) &&
						!empty($_POST['eventResponsibleChild'][$i]) && 
						!in_array($_POST['eventResponsibleChild'][$i],$userMessages)
					) {
						$userMessages[] = $_POST['eventResponsibleChild'][$i];
					}
						
					++$i;
				}
			}
			
			// Följdfråga spara
			if(!empty($eventsChild))
				$this->eventanalysislib->insertEventAnalysisQuestions($eventsChild);
			
			// Händelseanalys: Åtgärdslista
			if(isset($_POST['eventAnswersDone']))
				$this->eventanalysislib->updateEventAnalysisComplete($answers[0],UUID_TO_BIN($ea_id));
			else
				$this->eventanalysislib->updateEventAnalysis($answers[0],UUID_TO_BIN($ea_id));
			
			// QA
			$eventsEdit = array();
			if(isset($_POST['eventIdEdit'])) {
				$i = 0;
				foreach($_POST['eventIdEdit'] as $id => $event) {
					$eventsEdit[$_POST['eventIdEdit'][$i]] = array(
						'question'		=> $_POST['eventQuestionEdit'][$i],
						'answer'		=> $_POST['eventAnswerEdit'][$i],
						'responsible'	=> isset($_POST['eventResponsibleEdit'][$i]) ? UUID_TO_BIN($_POST['eventResponsibleEdit'][$i]) : null
					);
					if (
						empty($_POST['eventAnswerEdit'][$i]) &&
						!empty($_POST['eventResponsibleEdit'][$i]) && 
						!in_array($_POST['eventResponsibleEdit'][$i],$userMessages)
					) {
						$userMessages[] = $_POST['eventResponsibleEdit'][$i];
					}
					
					++$i;
				}
			}
			// QA spara
			if(!empty($eventsEdit))
				$this->eventanalysislib->updateEventAnalysisQuestions($eventsEdit);
			
			// Nya följdfrågor till grundfråga
			$eventsChild = array();
			if(isset($_POST['eventIdEditChild'])) {
				$i = 0;
				foreach($_POST['eventIdEditChild'] as $id => $event) {
					$question_id = UUIDv4();
					$eventsChild[] = array(
						'id'			=> UUID_TO_BIN($question_id),
						'ea_id'			=> UUID_TO_BIN($ea_id),
						'parent'		=> UUID_TO_BIN($_POST['eventEditParent'][$i]),
						'question'		=> $_POST['eventQuestionEditChild'][$i],
						'answer'		=> $_POST['eventAnswerEditChild'][$i],
						'responsible'	=> isset($_POST['eventResponsibleEditChild'][$i]) ? UUID_TO_BIN($_POST['eventResponsibleEditChild'][$i]) : null
					);
					if (
						empty($_POST['eventAnswerEditChild'][$i]) &&
						!empty($_POST['eventResponsibleEditChild'][$i]) && 
						!in_array($_POST['eventResponsibleEditChild'][$i],$userMessages)
					) {
						$userMessages[] = $_POST['eventResponsibleEditChild'][$i];
					}

					++$i;
				}
			}
			
			// Nya följdfrågor till grundfråga: Spara
			if(!empty($eventsChild))
				$this->eventanalysislib->insertEventAnalysisQuestions($eventsChild);
			
			// Åtgärdslista: Insert
			// @STEP2: You have been made responsible
			$actionsInsert = array();
			if(isset($_POST['eventActionListName'])) {
				$i = 0;
				foreach($_POST['eventActionListName'] as $id => $event) {
					$action_id = UUIDv4();
					$actionsInsert[] = array(
						'id'			=> UUID_TO_BIN($action_id),
						'ea_id'			=> UUID_TO_BIN($ea_id),
						'name'			=> $_POST['eventActionListName'][$i],
						'description'	=> $_POST['eventActionListDescription'][$i],
						'responsible'	=> isset($_POST['eventActionListResponsible'][$i]) ? UUID_TO_BIN($_POST['eventActionListResponsible'][$i]) : null,
						'done'			=> $_POST['eventActionListDone'][$i],
					);
					++$i;
				}
			}
			// Åtgärdslista spara
			if(!empty($actionsInsert))
				$this->eventanalysislib->insertEventAnalysisActionList($actionsInsert);
			
			// Åtgärdslista: Update
			$actionsUpdate = array();
			if(isset($_POST['eventActionListIdEdit'])) {
				$i = 0;
				foreach($_POST['eventActionListIdEdit'] as $id => $event) {
					$actionsUpdate[$_POST['eventActionListIdEdit'][$i]] = array(
						'name'			=> $_POST['eventActionListNameEdit'][$i],
						'description'	=> $_POST['eventActionListDescriptionEdit'][$i],
						'responsible'	=> isset($_POST['eventActionListResponsibleEdit'][$i]) ? UUID_TO_BIN($_POST['eventActionListResponsibleEdit'][$i]) : null,
						'done'			=> isset($_POST['eventActionListDoneEdit'][$i]) ? $_POST['eventActionListDoneEdit'][$i] : 0,
					);
					++$i;
				}
			}
			
			// Åtgärdslista uppdatera
			if(!empty($actionsUpdate))
				$this->eventanalysislib->updateEventAnalysisActionList($actionsUpdate);

			$this->load->model('user_messages_model');
			$this->user_messages_model->remove_all_by_id('eventanalysis',$ea_id,'qa');
			$this->user_messages_model->remove_all_by_id('eventanalysis_actionlist',$ea_id,'actionlist');

			// var_dump($actionsInsert,$actionsUpdate);exit;
			
			// @STEP2: create_batch and save them
			if(!empty($userMessages)) {
				foreach($userMessages AS $user) {
					$this->user_messages_model->create(
						$user,
						'eventanalysis',
						$ea_id,
						'qa',
						'warning'
					);
				}
			} elseif (empty($actionsInsert) && empty($actionsUpdate)) {
				if (isset($this->users[$getEventAnalysis->us_id])) {
					$this->user_messages_model->create(
						$getEventAnalysis->us_id,
						'eventanalysis_actionlist',
						$ea_id,
						'actionlist',
						'warning'
					);
				}
			}

			// @STEP2: User have been assigned an action
			
			redirect('eventanalysis/view/' . $ea_id); exit;
		}
		else
		{
			$this->data['fields'] = array(
				array(
					'id' => 'name',
					'input' => 'input',
					'required' => 1,
					'title' => 'Rubrik',
					'description' => 'Ange kortfattat vad som rapporteras.',
					'value' => $getEventAnalysis->name,
					'values' => null,
				),
				array(
					'id' => 'redo',
					'input' => 'text',
					'required' => 1,
					'title' => 'Redogörelse',
					'description' => 'Fördjupad redogörelse.',
					'value' => $getEventAnalysis->redo,
					'values' => null,
				)
			);
			
			if( $this->auth_kiv )
			{
				$this->data['fields'] = array_merge($this->data['fields'], array(
					array(
						'id' => 'us_id',
						'input' => 'users',
						'required' => 0,
						'title' => 'Användare',
						'description' => 'Vem är ansvarig för händelseanalysen?',
						'value' => $getEventAnalysis->us_id,
						'values' => $this->users
					),
				));
			}
			
			$this->data['fieldsDone'] = array(
				array(
					'id' => 'plan',
					'input' => 'text',
					'required' => 1,
					'title' => 'Handlingsplan',
					'description' => 'Hur skall ni se till att händelsen inte upprepas?',
					'value' => $getEventAnalysis->plan,
					'values' => null,
				),
				array(
					'id' => 'done',
					'input' => 'date',
					'required' => 0,
					'title' => 'Datum',
					'description' => 'Händelseanalysen är färdig.',
					'value' => $getEventAnalysis->done?$getEventAnalysis->done:date('Y-m-d'),
					'values' => null,
				)
			);
			
			$this->data['eventFields'] = array(
				'question' => array(
					'type' => 'input',
					'name' => 'eventQuestion[]',
					'class' => 'required form-control eventQuestion',
					'placeholder' => 'Fråga',
					'title' => 'Fråga',
				),
				'answer' => array(
					'type' => 'input',
					'name' => 'eventAnswer[]',
					'class' => 'form-control eventAnswer',
					'placeholder' => 'Svar',
					'title' => 'Svar',
				),
			);
			
			if( $this->auth_kiv )
			{
				$this->data['eventFields'] = array_merge($this->data['eventFields'], array(
					'responsible' => array(
						'type' => 'users',
						'name' => 'eventResponsible[]',
						'class' => 'required form-control eventResponsible',
						'values' => $this->users,
						'title' => 'Vem skall besvara frågan?',
					),
				));
			}
			
			$this->data['actionListFields'] = array(
				'name' => array(
					'type' => 'input',
					'name' => 'eventActionListName[]',
					'class' => 'required',
					'title' => 'Åtgärd',
					'placeholder' => 'Åtgärd'
				),
			);
			
			if( $this->auth_kiv )
			{
				$this->data['actionListFields'] = array_merge($this->data['actionListFields'], array(
					'responsible' => array(
						'type' => 'users',
						'name' => 'eventActionListResponsible[]',
						'class' => 'required',
						'values' => $this->users,
						'title' => 'Vem är ansvarig?',
					),
				));
			}
			
			$this->data['actionListFields'] = array_merge($this->data['actionListFields'], array(
				'description' => array(
					'type' => 'textarea',
					'name' => 'eventActionListDescription[]',
					'title' => 'Beskrivning',
					'placeholder' => 'Förklarande text...'
				),
				'done' => array(
					'title' => 'Är åtgärden färdig?',
					'type' => 'checkbox',
					'name' => 'eventActionListDone[]',
					'values' => 1,
					'title' => 'Är åtgärden färdig?',
				)
			));
			
			$this->load->view('general/eventanalysis/update',$this->data);
		}
	}
	
	public function view( $ea_id = NULL )
	{
		$this->VALID_UUIDv4($ea_id);
		$this->data['ea_id'] = $ea_id;
		
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['create']) ) { show_404(); }
		
		$getEventAnalysis = $this->eventanalysislib->getEventAnalysis($ea_id);
		// var_dump($getEventAnalysis);exit;
		if( empty($getEventAnalysis) OR $getEventAnalysis->company_id !== $this->auth_company_id )
			show_404();
		list($getEventAnalysisQuestions,$getEventAnalysisQuestionsAnswers) = $this->eventanalysislib->getEventAnalysisQuestions($ea_id);
		$getEventAnalysisActionList = $this->eventanalysislib->getEventAnalysisActionList($ea_id);
		
		$getEventAnalysisDeviationMap       = $this->eventanalysislib->getEventAnalysisDeviationMap($ea_id);
		$getEventAnalysisRiskAssessmentsMap = $this->eventanalysislib->getEventAnalysisRiskAssessmentsMap($ea_id);
		$getDeviationNames                  = $this->eventanalysislib->getDeviationNames($this->data['rights']['create'],$getEventAnalysisDeviationMap,$this->auth_company_id);
		$getRiskAnalysisNames               = $this->eventanalysislib->getRiskAnalysisNames($this->auth_company_id);
		
		$this->data['getEventAnalysis']                   = $getEventAnalysis;
		$this->data['getEventAnalysisQuestions']          = $getEventAnalysisQuestions;
		$this->data['getEventAnalysisQuestionsAnswers']   = $getEventAnalysisQuestionsAnswers;
		$this->data['getEventAnalysisActionList']         = $getEventAnalysisActionList;
		$this->data['getEventAnalysisDeviationMap']       = $getEventAnalysisDeviationMap;
		$this->data['getDeviationNames']                  = $getDeviationNames;
		$this->data['getEventAnalysisRiskAssessmentsMap'] = $getEventAnalysisRiskAssessmentsMap;
		$this->data['getRiskAnalysisNames']               = $getRiskAnalysisNames;
		
		$this->data['fields'] = array(
			array(
				'id' => 'name',
				'input' => 'input',
				'required' => 1,
				'title' => 'Rubrik',
				'description' => 'Ange kortfattat vad som rapporteras.',
				'value' => $getEventAnalysis->name,
				'values' => null,
			),
			array(
				'id' => 'redo',
				'input' => 'text',
				'required' => 1,
				'title' => 'Redogörelse',
				'description' => 'Fördjupad redogörelse.',
				'value' => $getEventAnalysis->redo,
				'values' => null,
			)
		);
		
		if( $this->auth_kiv )
		{
			$this->data['fields'] = array_merge($this->data['fields'], array(
				array(
					'id' => 'us_id',
					'input' => 'users',
					'required' => 0,
					'title' => 'Användare',
					'description' => 'Vem är ansvarig för händelseanalysen?',
					'value' => $getEventAnalysis->us_id,
					'values' => $this->users
				),
			));
		}
		
		$this->data['fieldsDone'] = array(
			array(
				'id' => 'plan',
				'input' => 'text',
				'required' => 1,
				'title' => 'Handlingsplan',
				'description' => 'Hur skall ni se till att händelsen inte upprepas?',
				'value' => $getEventAnalysis->plan
			),
			array(
				'id' => 'done',
				'input' => 'date',
				'required' => 0,
				'title' => 'Datum',
				'description' => 'Händelseanalysen är färdig.',
				'value' => $getEventAnalysis->done
			)
		);
		
		$this->data['eventFields'] = array(
			'question' => array(
				'type' => 'input',
				'name' => 'eventQuestion[]',
				'class' => 'required form-control eventQuestion',
				'placeholder' => 'Fråga',
				'title' => 'Fråga',
			),
			'answer' => array(
				'type' => 'input',
				'name' => 'eventAnswer[]',
				'class' => 'form-control eventAnswer',
				'placeholder' => 'Svar',
				'title' => 'Svar',
			),
		);
		
		if( $this->auth_kiv )
		{
			$this->data['eventFields'] = array_merge($this->data['eventFields'], array(
				'responsible' => array(
					'type' => 'users',
					'name' => 'eventResponsible[]',
					'class' => 'required form-control eventResponsible',
					'values' => $this->users,
					'title' => 'Vem skall besvara frågan?',
				),
			));
		}
		
		$this->data['actionListFields'] = array(
			'name' => array(
				'type' => 'input',
				'name' => 'eventActionListName[]',
				'class' => 'required',
				'title' => 'Åtgärd',
				'placeholder' => 'Åtgärd'
			),
		);
		
		if( $this->auth_kiv )
		{
			$this->data['actionListFields'] = array_merge($this->data['actionListFields'], array(
				'responsible' => array(
					'type' => 'users',
					'name' => 'eventActionListResponsible[]',
					'class' => 'required',
					'values' => $this->users,
					'title' => 'Vem är ansvarig?',
				),
			));
		}
		
		$this->data['actionListFields'] = array_merge($this->data['actionListFields'], array(
			'description' => array(
				'type' => 'textarea',
				'name' => 'eventActionListDescription[]',
				'title' => 'Beskrivning',
				'placeholder' => 'Förklarande text...'
			),
			'done' => array(
				'title' => 'Är åtgärden färdig?',
				'type' => 'checkbox',
				'name' => 'eventActionListDone[]',
				'values' => 1,
				'title' => 'Är åtgärden färdig?',
			)
		));
		
		$this->load->view('general/eventanalysis/view',$this->data);
	}
	
	public function saveqa($ea_id = NULL)
	{
		$this->VALID_UUIDv4($ea_id);
		
		$getEventAnalysis = $this->eventanalysislib->getEventAnalysis($ea_id);
		if( empty($getEventAnalysis) OR $getEventAnalysis->company_id !== $this->auth_company_id )
			show_404();
		
		list($getEventAnalysisQuestions,$getEventAnalysisQuestionsAnswers) = $this->eventanalysislib->getEventAnalysisQuestions($ea_id);
		
		if( $this->input->method(TRUE) === 'POST' )
		{
			// QA
			if(isset($_POST['eventIdEdit'])) {
				$eventsEdit = array();
				$i = 0;
				foreach($_POST['eventIdEdit'] as $id => $event) {
					$eventsEdit[$_POST['eventIdEdit'][$i]] = array(
						'answer' => $_POST['eventAnswerEdit'][$i],
					);
					++$i;
				}
			}

			// QA spara
			if(!empty($eventsEdit))
				$this->eventanalysislib->updateEventAnalysisQuestions($eventsEdit,true);
			
			$this->load->model(['eventanalysis_model','user_messages_model']);
			$answers = $this->eventanalysis_model->get_empty_answers($ea_id);
			if( ! isset($answers[$this->auth_user_id]) )
			{
				$this->user_messages_model->remove( 'eventanalysis', $this->auth_user_id, $ea_id );
			}
			if( empty($answers) )
			{
				if (isset($this->users[$getEventAnalysis->us_id])) {
					$this->user_messages_model->create(
						$getEventAnalysis->us_id,
						'eventanalysis_actionlist',
						$ea_id,
						'actionlist',
						'warning'
					);
				}
			}
			
			redirect('eventanalysis/view/' . $ea_id); exit;
		}
		
		$this->data['fields'] = array(
			array(
				'id' => 'name',
				'input' => 'input',
				'required' => 1,
				'title' => 'Rubrik',
				'description' => 'Ange kortfattat vad som rapporteras.',
				'value' => $getEventAnalysis->name,
				'values' => null,
			),
			array(
				'id' => 'redo',
				'input' => 'text',
				'required' => 1,
				'title' => 'Redogörelse',
				'description' => 'Fördjupad redogörelse.',
				'value' => $getEventAnalysis->redo,
				'values' => null,
			)
		);
		
		if( $this->auth_kiv )
		{
			$this->data['fields'] = array_merge($this->data['fields'], array(
				array(
					'id' => 'us_id',
					'input' => 'users',
					'required' => 0,
					'title' => 'Användare',
					'description' => 'Vem är ansvarig för händelseanalysen?',
					'value' => $getEventAnalysis->us_id,
					'values' => $this->users
				),
			));
		}
		
		$this->data['fieldsDone'] = array(
			array(
				'id' => 'plan',
				'input' => 'text',
				'required' => 1,
				'title' => 'Handlingsplan',
				'description' => 'Hur skall ni se till att händelsen inte upprepas?',
				'value' => $getEventAnalysis->plan
			),
			array(
				'id' => 'done',
				'input' => 'date',
				'required' => 0,
				'title' => 'Datum',
				'description' => 'Händelseanalysen är färdig.',
				'value' => $getEventAnalysis->done
			)
		);
		
		$this->data['eventFields'] = array(
			'question' => array(
				'type' => 'input',
				'name' => 'eventQuestion[]',
				'class' => 'required form-control eventQuestion',
				'placeholder' => 'Fråga',
				'title' => 'Fråga',
			),
			'answer' => array(
				'type' => 'input',
				'name' => 'eventAnswer[]',
				'class' => 'form-control eventAnswer',
				'placeholder' => 'Svar',
				'title' => 'Svar',
			),
		);
		
		if( $this->auth_kiv )
		{
			$this->data['eventFields'] = array_merge($this->data['eventFields'], array(
				'responsible' => array(
					'type' => 'users',
					'name' => 'eventResponsible[]',
					'class' => 'required form-control eventResponsible',
					'values' => $this->users,
					'title' => 'Vem skall besvara frågan?',
				),
			));
		}
		
		$this->data['getEventAnalysis']           = $getEventAnalysis;
		$this->data['getEventAnalysisQuestions']  = $getEventAnalysisQuestions;
		
		$this->load->view('general/eventanalysis/saveqa',$this->data);
	}
	
	public function connect($ea_id = NULL)
	{
		$this->VALID_UUIDv4($ea_id);
		
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }
		
		$getEventAnalysis = $this->eventanalysislib->getEventAnalysis($ea_id);
		if( empty($getEventAnalysis) OR $getEventAnalysis->company_id !== $this->auth_company_id )
			show_404();
		
		if( $this->input->method(TRUE) === 'POST' )
		{
			$deviation = array();
			if(isset($_POST['deviation'])) {
				foreach($_POST['deviation'] AS $value) {
					$deviation[] = array(
						'a_id'			=> UUID_TO_BIN($value),
						'ea_id'			=> UUID_TO_BIN($ea_id)
					);
				}
			}
			
			// Spara
			$this->eventanalysislib->insertEventAnalysisDeviationMap($deviation,$ea_id);
			
			$riskassesments = array();
			if(isset($_POST['riskassesments'])) {
				foreach($_POST['riskassesments'] AS $value) {
					$riskassesments[] = array(
						'ra_id'			=> UUID_TO_BIN($value),
						'ea_id'			=> UUID_TO_BIN($ea_id)
					);
				}
			}
			
			// Spara
			$this->eventanalysislib->insertEventAnalysisRiskAssessmentsMap($riskassesments,$ea_id);
			
			redirect('eventanalysis/view/' . $ea_id); exit;
		}
		
		$this->data['getEventAnalysis'] = $getEventAnalysis;
		
		$this->data['getEventAnalysisDeviationMap']       = $this->eventanalysislib->getEventAnalysisDeviationMap($ea_id);
		$this->data['getDeviationNames']                  = $this->eventanalysislib->getDeviationNames($this->data['rights']['update'],$this->data['getEventAnalysisDeviationMap'],$this->auth_company_id);
		$this->data['getEventAnalysisRiskAssessmentsMap'] = $this->eventanalysislib->getEventAnalysisRiskAssessmentsMap($ea_id);
		$this->data['getRiskAnalysisNames']               = $this->eventanalysislib->getRiskAnalysisNames($this->auth_company_id);
		
		$this->data['deviationFields'] = array(
			'deviation' => array(
				'type' => 'select-multiple',
				'id' => 'deviation',
				'name' => 'deviation[]',
				'class' => 'required',
				'values' => array('' => 'Välj avvikelse'),
				'placeholder' => 'Välj avvikelse',
				'prepend' => '<label>Anslut avvikelse</label>'
			),
			'riskassesments' => array(
				'type' => 'select-multiple',
				'id' => 'riskassesments',
				'name' => 'riskassesments[]',
				'class' => 'required',
				'values' => array('' => 'Välj riskbedömning'),
				'placeholder' => 'Välj riskbedömning',
				'prepend' => '<label>Anslut riskbedömning</label>'
			)
		);
		
		$this->data['deviationFields']['deviation']['values']             += $this->data['getDeviationNames'];
		$this->data['deviationFields']['deviation']['multiple-value']      = $this->data['getEventAnalysisDeviationMap'];
		$this->data['deviationFields']['riskassesments']['values']        += $this->data['getRiskAnalysisNames'];
		$this->data['deviationFields']['riskassesments']['multiple-value'] = $this->data['getEventAnalysisRiskAssessmentsMap'];
		
		$this->load->view('general/eventanalysis/connect',$this->data);
	}
	
	public function display()
	{
		$this->load->model(['report_model']);
		$this->data['rights'] = $this->acl['deviation'];
		$this->data['events'] = $this->eventanalysislib->getAll($this->auth_company_id);
		if( $this->auth_kiv )
		{
			$this->data['report']['eventanalysis']['critical'] = $this->report_model->eventanalysis_by_id( array_keys($this->data['report']['messages']['eventanalysis']['critical']) );
		}
		else
		{
			$this->data['report']['eventanalysis']['critical'] = $this->report_model->eventanalysis( $this->data['rights']['create'] );
		}
		$this->load->view('general/eventanalysis/display',$this->data);
	}
	
	/**
	 * Track share action for event analysis
	 */
	public function track_share()
	{
		// Enable error logging and reporting for debugging
		log_message('debug', 'Event analysis track_share method started');
		
		// Allow both AJAX and regular POST requests
		if( $this->input->method(TRUE) !== 'POST' )
		{
			log_message('error', 'Track event analysis share: Not a POST request');
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Invalid request method'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$eventanalysis_id = $this->input->post('eventanalysis_id');
		$share_url = $this->input->post('share_url');

		// Log for debugging
		log_message('debug', 'Track event analysis share called with eventanalysis_id: ' . $eventanalysis_id . ', share_url: ' . $share_url . ', user_id: ' . $this->auth_user_id);

		if( empty($eventanalysis_id) || empty($share_url) )
		{
			log_message('error', 'Track event analysis share: Missing required parameters');
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Missing parameters'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		try {
			$this->VALID_UUIDv4($eventanalysis_id);
		} catch (Exception $e) {
			log_message('error', 'Track event analysis share: Invalid UUID - ' . $eventanalysis_id);
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Invalid ID'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Check if user is logged in (moved after initial validations)
		if( ! $this->auth_user_id )
		{
			log_message('error', 'Track event analysis share: User not logged in');
			$this->output
					->set_status_header(401)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Not authenticated'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Check if event analysis exists
		$this->load->model('eventanalysis_model');
		if( ! $this->eventanalysis_model->get_eventanalysis_exists($eventanalysis_id) )
		{
			log_message('error', 'Track event analysis share: Event analysis not found - ' . $eventanalysis_id);
			$this->output
					->set_status_header(404)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Event analysis not found'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Load share model
		$this->load->model('share_model');
		log_message('debug', 'Share model loaded successfully for event analysis');

		// Check if event analysis is already shared
		$is_shared = $this->share_model->is_entity_shared('eventanalysis', $eventanalysis_id);
		log_message('debug', 'Event analysis shared status: ' . ($is_shared ? 'true' : 'false'));

		if( ! $is_shared )
		{
			// Add new share record
			log_message('debug', 'Adding new event analysis share record');
			$share_id = $this->share_model->add_share('eventanalysis', $eventanalysis_id, NULL, $this->auth_user_id);

			if( $share_id )
			{
				log_message('debug', 'Event analysis share added successfully with ID: ' . $share_id);
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['success' => TRUE, 'share_id' => $share_id], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
			else
			{
				log_message('error', 'Failed to add event analysis share record');
			}
		}
		else
		{
			// Event analysis already shared - track this share action
			log_message('debug', 'Updating existing event analysis share tracking');
			$updated = $this->share_model->update_share_date('eventanalysis', $eventanalysis_id);

			if( $updated )
			{
				log_message('debug', 'Event analysis share tracking updated successfully');
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['success' => TRUE, 'message' => 'Event analysis share tracked'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
			else
			{
				log_message('error', 'Failed to update event analysis share tracking');
			}
		}

		$this->output
				->set_status_header(500)
				->set_content_type('application/json', 'utf-8')
				->set_output(json_encode(['success' => FALSE, 'message' => 'Event analysis share failed'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
				->_display();
		exit;
	}

}