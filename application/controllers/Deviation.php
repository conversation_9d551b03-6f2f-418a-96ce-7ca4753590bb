<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Deviation extends User_Controller
{
	private $pages = [1,2,3];

	public function __construct()
	{
		parent::__construct();
		$this->data['sidebar']['module'] = 'deviation';
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model(array('deviation_model'));
		$db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('deviationlib',$db);
		$this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
		$this->departments = '';
	}
	// @STEP2: Delete uploaded files if user exit.
	public function add( $page = NULL, $a_id = NULL )
	{
		if( ! in_array($page,$this->pages) ) { show_404(); }
		$this->data['page'] = $page;

		$this->VALID_UUIDv4($a_id,FALSE);
		if( $a_id !== NULL)
		{
			$departments = $this->deviation_model->deviationDepartment($a_id);
			if ($page == 2 && !acl_deviation_permits('deviation_two', $departments)) {
				redirect('deviation/view/' . $a_id); exit;
			}
			if ($page == 3 && !acl_deviation_permits('deviation_three', $departments)) {
				redirect('deviation/view/' . $a_id); exit;
			}
			$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($a_id);
			if( empty($this->data['deviation']['registred']) ) { show_404(); }
			$this->data['uuid_kvalprak'] = $a_id;
		}
		else
		{
			// Get a_id from POST, so that attachments and text are added to the same document.
			// @STEP2: Store it in $_SESSION and match it for security
			if( $this->input->method(TRUE) === 'POST' )
			{
				if( $a_id = $this->input->post('uuid_kvalprak') )
				{
					if( $this->VALID_UUIDv4($a_id) )
					{
						$this->data['uuid_kvalprak'] = $a_id;
						// @STEP2: Get attachments if required fields fail
					}
				}
			}
			else
			{
				$this->data['uuid_kvalprak'] = $a_id = UUIDv4();
			}
		}

		$this->load->model(array('group_model'));
		$this->data['rights'] = $this->acl['deviation'];

		// $fields = [ 'all' => [...], 'type' => [...] ]
		$this->data['fields'] = $this->deviation_model->getDeviationFieldsByPage($page);

		if ($page > 1) {
			$this->data['deviation_page1_fields'] = $this->deviation_model->getDeviationPageAndOrId($a_id,$this->data['rights']['update'], 1);
			$this->data['fields_page1'] = $this->deviation_model->getDeviationFieldsByPage(1);
			$this->data['options_page1'] = $this->deviation_model->getDropdown();
			$this->_setOptions($this->data['fields_page1'], $this->data['options_page1']);
		}

		if ($page > 2) {
			$this->data['deviation_page2_fields'] = $this->deviation_model->getDeviationPageAndOrId($a_id,$this->data['rights']['update'], 2);
			$this->data['fields_page2'] = $this->deviation_model->getDeviationFieldsByPage(2);
			$this->data['options_page2'] = $this->deviation_model->getDropdown();
			$this->_setOptions($this->data['fields_page2'], $this->data['options_page2']);
		}

		if( $this->input->method(TRUE) === 'POST' )
		{
			// @TODO: Flashmessages
			if( $page === 2 && ! empty($this->data['deviation']['registred']->regby_two) )
			{
				redirect('deviation/view/' . $a_id); exit;
			}
			// @TODO: Flashmessages
			if( $page === 3 && ! empty($this->data['deviation']['registred']->regby_three) )
			{
				redirect('deviation/view/' . $a_id); exit;
			}

			$departments = $this->input->post('department[]');

			if( $this->deviation_model->save($a_id, $page) === FALSE ) {}

			if( $this->deviation_model->save_answers($a_id, $this->data['fields']['all']) === FALSE ) {}

			if( $this->deviation_model->save_departments($a_id) === FALSE ) {}

			if( $this->deviation_model->save_fields($a_id, $this->data['fields']['all']) === FALSE ) {}

			if( $address = $this->deviation_model->save_emails($a_id, $departments) ) {
				if( ! empty($address) )
				{
					$this->_send_email($address, $page, $a_id, $this->deviation_model->getDeviationName($a_id));
				}
			} else {
				$old_emails = $this->deviation_model->get_email($a_id);
				$old_address = array();
				foreach($old_emails as $field_id=>$old_users) {
					foreach($old_users as $user_id) {
						$old_address[$this->users[$user_id]->email] = $this->users[$user_id]->name;
					}
				}
				if( ! empty($old_address) )
				{
					$this->_send_email($old_address, $page, $a_id, $this->deviation_model->getDeviationName($a_id));
				}
			}

			// @TODO: Send email to the person who created the devion when it's done

			// Redirect
			if( $this->input->post('form_next') )
			{
				if( empty($departments) )
				{
					$departments = $this->deviation_model->deviationDepartment($a_id);
				}
	
				$next_page = $page + 1;
				if($next_page <= 3)
				{
					if(
						( $next_page == 2 && acl_deviation_permits('deviation_two', $departments) ) OR
						( $next_page == 3 && acl_deviation_permits('deviation_three', $departments) )
					)
					{
						redirect('deviation/add/' . $next_page . '/' . $a_id); exit;
					}
				}
				else
				{
					if(	$this->data['deviation']['registred']->eventanalysis === $this->auth_user_id )
					{
						redirect('eventanalysis'); exit;
					}
				}

				switch($page)
				{
					default:
					$message = '
						<h2>Avvikelserapport</h2>
						<h3>Steg 1 av 3 är slutförd</h3>
						<p>Avvikelse ID: '.$a_id.'</p>
						<p>Steg 2 och 3 behöver kompletteras av dina kollegor.</p>';
					break;
					case '2':
					$message = '
						<h2>Avvikelserapport</h2>
						<h3>Steg 2 av 3 är slutförd</h3>
						<p>Avvikelse ID: '.$a_id.'</p>
						<p>Steg 3 behöver kompletteras av dina kollegor.</p>';
					break;
					case '3':
					$message = '
						<h2>Avvikelserapport</h2>
						<h3>Steg 3 av 3 är slutförd</h3>
						<p>Avvikelse ID: '.$a_id.'</p>';
					break;
				}

				$_SESSION['flashdata'] = $message;

				redirect('deviation'); exit;
			}
			// Not in use
			if( $this->input->post('form_previous') )
			{
				$previous_page = $page - 1;
				if($previous_page <= 3 && $previous_page >= 1)
				{
					redirect('deviation/viewpage/' . $previous_page . '/' . $a_id); exit;
				}
				else
				{
					redirect('deviation'); exit;
				}
			}
			// No button pressed???
			redirect('deviation'); exit;
		}
		else
		{
			$this->data['options']  = $this->deviation_model->getDropdown();
			$this->data['groups']   = $this->group_model->get_all();
			$this->data['selected'] = [];
			if( empty($this->data['rights']['create']) ) { show_404(); }

			$this->_setOptions($this->data['fields'], $this->data['options']);

			$emailId = NULL;
			if( ! empty($this->data['fields']['type']['email']) )
			{
				foreach($this->data['fields']['type']['email'] as $email)
				{
					$emailId = $email;
				}
			}

			// Show preselected email
			if( $emailId !== NULL )
			{
				if( $page != 1 )
				{
					$departments = $this->deviation_model->deviationDepartment($a_id);
				}
				elseif( count($this->data['rights']['create']) === 1 )
				{
					$departments = $this->data['rights']['create'];
				}
				if( ! empty($departments) )
				{
					$this->data['selected'][$emailId] = $this->deviation_model->get_email_default( $emailId, $departments );
				}
			}
			// Contact information
			$this->load->view('general/deviation/create',$this->data);
		}
	}

	private function _send_email($address, $page, $a_id, $title)
	{
		// Send email
		$this->data['a_id']          = $a_id;
		$this->data['title']         = $title;
		$this->data['page']          = $page;
		$this->data['created_by']    = $this->auth_name;
		$this->data['deviation_url'] = site_url('deviation/viewpage/1/' . $a_id);
		$this->data['completed_url'] = site_url('deviation/view/' . $a_id);

		$this->load->library('PHPMailerLib');
		$mail = $this->phpmailerlib->load();
		$mail->Subject = $this->config->item('program_name') . ': Avvikelserapport (sida ' . $page . ')';
		$mail->Body = $this->load->view('general/deviation/send_email',$this->data,TRUE);

		foreach($address as $email => $name)
		{
			$mail->addAddress($email,$name);
		}

		$mail->send();
	}

	public function view( $id )
	{
		$this->VALID_UUIDv4($id);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['create']) ) { show_404(); }

		$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($id);
		if( empty($this->data['deviation']['registred']) ) { show_404(); }

		if( $this->data['deviation']['registred']->regby_one === $this->auth_user_id )
			$this->data['deviation']['fields'] = $this->deviation_model->getDeviationPageAndOrId($id);
		elseif( ! empty($this->data['rights']['read']) )
			$this->data['deviation']['fields'] = $this->deviation_model->getDeviationPageAndOrId($id,$this->data['rights']['read']);
		else
			show_404();

		if( empty($this->data['deviation']['fields']) ) { show_404(); }

		$this->data['getDeviationEventAnalysisMap']  = $this->deviationlib->getDeviationEventAnalysisMap($id);
		$this->data['getDeviationRiskAssesmentsMap'] = $this->deviationlib->getDeviationRiskAssesmentsMap($id);
		$this->data['getEventAnalysisNames']         = $this->deviationlib->getEventAnalysisNames($this->auth_company_id);
		$this->data['getRiskAssessmentsNames']       = $this->deviationlib->getRiskAssessmentsNames($this->auth_company_id);

		$this->data['a_id'] = $id;
		$this->data['attachments'] = $this->deviation_model->get_attachments($id);
		$this->data['fields']      = $this->deviation_model->getDeviationFieldsByPage(array(1,2,3));
		$this->data['options']     = $this->deviation_model->getDropdown();
		$this->data['groups']      = $this->group_model->get_all();
		$this->data['selected']    = [];

		$departments = $this->deviation_model->deviationDepartment($id);
		$this->data['rightsPage2'] = acl_deviation_permits('deviation_two', $departments);
		$this->data['rightsPage3'] = acl_deviation_permits('deviation_three', $departments);

		$this->_setOptions($this->data['fields'], $this->data['options']);
		$this->_setSelected($this->data['fields'], $this->data['selected'], $departments, $id);


		$this->load->view('general/deviation/view',$this->data);
	}

	public function viewpage( $page = NULL, $id = NULL )
	{
		$this->VALID_UUIDv4($id);

		if( ! in_array($page,$this->pages) ) { show_404(); }
		$this->data['page'] = $page = intval($page);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['create']) ) { show_404(); }

		$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($id);
		if( empty($this->data['deviation']['registred']) ) { show_404(); }

		if( ! empty($this->data['deviation']['registred']->regby_three) )
		{
			redirect('deviation/view/' . $id); exit;
		}

		$departments = $this->deviation_model->deviationDepartment($id);

		if($page === 1 && !acl_deviation_permits('deviation_two', $departments))
		{
			redirect('deviation'); exit;
		}

		if($page === 2 && !acl_deviation_permits('deviation_three', $departments))
		{
			redirect('deviation'); exit;
		}

		if( $this->input->method(TRUE) === 'POST' )
		{
			if( $this->input->post('form_next') )
			{
				$next_page = $page + 1;

				if( $this->data['deviation']['registred']->regby_two === NULL && acl_deviation_permits('deviation_two', $departments) ) {
					redirect('deviation/add/2/' . $id); exit;
				}
				if( $page === 2 && $this->data['deviation']['registred']->regby_three === NULL && acl_deviation_permits('deviation_three', $departments) ) {
					redirect('deviation/add/3/' . $id); exit;
				}
				if( $next_page <= 3 && ( acl_deviation_permits('deviation_two', $departments) OR acl_deviation_permits('deviation_three', $departments) ) ) {
					redirect('deviation/viewpage/' . $next_page . '/' . $id); exit;
				}

				redirect('deviation'); exit;
			}
			if( $this->input->post('form_previous') )
			{
				$previous_page = $page - 1;
				if($previous_page <= 3 && $previous_page >= 1) {
					redirect('deviation/viewpage/' . $previous_page . '/' . $id); exit;
				}
			}
			redirect('deviation'); exit;
		}
		else
		{
			$this->data['deviation']['fields'] = $this->deviation_model->getDeviationPageAndOrId($id,$this->data['rights']['update'],$page);
			if( empty($this->data['deviation']['fields']) ) { show_404(); }

			$this->data['options']  = $this->deviation_model->getDropdown();
			$this->data['a_id']     = $id;
			$this->data['fields']   = $this->deviation_model->getDeviationFieldsByPage($page);
			$this->data['groups']   = $this->group_model->get_all();
			$this->data['selected'] = [];

			$this->_setOptions($this->data['fields'], $this->data['options']);
			$this->_setSelected($this->data['fields'], $this->data['selected'], $departments, $id);

			$this->load->view('general/deviation/viewpage',$this->data);
		}
	}
	// @STEP2: Deviation view department
	// @STEP2: View deviation_email
	public function edit( $page = NULL, $id = NULL )
	{
		$this->VALID_UUIDv4($id);

		if( ! in_array($page,$this->pages) ) { show_404(); }
		$this->data['page'] = $page;

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($id);
		if( empty($this->data['deviation']['registred']) ) { show_404(); }

		$departments = $this->deviation_model->deviationDepartment($id);
		if ($page == 2 && !acl_deviation_permits('deviation_two', $departments)) {
			redirect('deviation/view/' . $id); exit;
		}
		if ($page == 3 && !acl_deviation_permits('deviation_three', $departments)) {
			redirect('deviation/view/' . $id); exit;
		}

		// $fields = [ 'all' => [...], 'type' => [...] ]
		$this->data['fields'] = $this->deviation_model->getDeviationFieldsByPage($page);

		// UUID for Dropzone
		$this->data['uuid_kvalprak'] = $id;

		if ($page > 1) {
			$this->data['deviation_page1_fields'] = $this->deviation_model->getDeviationPageAndOrId($id,$this->data['rights']['update'], 1);
			$this->data['fields_page1'] = $this->deviation_model->getDeviationFieldsByPage(1);
			$this->data['options_page1'] = $this->deviation_model->getDropdown();
			$this->_setOptions($this->data['fields_page1'], $this->data['options_page1']);
		}

		if ($page > 2) {
			$this->data['deviation_page2_fields'] = $this->deviation_model->getDeviationPageAndOrId($id,$this->data['rights']['update'], 2);
			$this->data['fields_page2'] = $this->deviation_model->getDeviationFieldsByPage(2);
			$this->data['options_page2'] = $this->deviation_model->getDropdown();
			$this->_setOptions($this->data['fields_page2'], $this->data['options_page2']);
		}

		if( $this->input->method(TRUE) === 'POST' )
		{
			if( $this->deviation_model->save_answers($id, $this->data['fields']['all']) === FALSE ) {}

			// @TODO: Flashmessages

			// Redirect
			if( $this->input->post('form_next') )
			{
				$next_page = $page + 1;
				if($next_page <= 3)
				{
					if (($next_page == 3 && !$this->data['deviation']['registred']->regby_three) || 
						($next_page == 2 && !$this->data['deviation']['registred']->regby_two)
					) {
						redirect('deviation/add/' . $next_page . '/' . $id); exit;
					} else {
						redirect('deviation/edit/' . $next_page . '/' . $id); exit;
					}
				}
				else
				{
					redirect('deviation/view/' . $id); exit;
				}
			}
			if( $this->input->post('form_previous') )
			{
				$previous_page = $page - 1;
				if($previous_page <= 3 && $previous_page >= 1)
				{
					redirect('deviation/edit/' . $previous_page . '/' . $id); exit;
				}
				else
				{
					redirect('deviation/view/' . $id); exit;
				}
			}
			// No button pressed???
			redirect('deviation'); exit;
		}
		else
		{
			$this->data['deviation']['fields'] = $this->deviation_model->getDeviationPageAndOrId($id,$this->data['rights']['update'],$page);
			if( empty($this->data['deviation']['fields']) ) { show_404(); }

			$this->data['options'] = $this->deviation_model->getDropdown();
			$this->data['a_id'] = $id;

			$this->_setOptions($this->data['fields'], $this->data['options']);

			$this->load->view('general/deviation/update',$this->data);
		}
	}

	public function attachments($id = NULL)
	{
		$this->VALID_UUIDv4($id);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($id);
		if( empty($this->data['deviation']['registred']) ) { show_404(); }

		$this->data['a_id'] = $this->data['uuid_kvalprak'] = $id;
		$this->data['attachments'] = $this->deviation_model->get_attachments($id);

		$this->load->view('general/deviation/attachments',$this->data);
	}

	public function upload()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		// @STEP2: Store it in $_SESSION and match it for security
		if( $a_id = $this->input->post('uuid_kvalprak') )
		{
			if( ! VALID_UUIDv4($a_id) )
			{
				$this->output
						->set_output( 'Okänt fel' )
						->set_status_header(400)
						->_display();
				exit;
			}
			// Upload files
			$upload_base_path = CI_UPLOAD_PATH . 'deviation';
			$dir_exists       = TRUE;
			$upload_data      = array();
			if( !empty($_FILES['file']['name']) && $upload_base_path !== FALSE )
			{
				// Create a folder for your company in case it dosen't exists
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				if( !is_dir($upload_path) )
					$dir_exists = mkdir($upload_path, 0777);

				if( $dir_exists )
				{
					// Generate a unique ID for attached file
					$attachment_id = UUIDv4();

					// File upload configuration
					$config['upload_path'] = $upload_path;
					$config['allowed_types'] = $this->config->item('allowed_types');
					$config['file_name'] = $attachment_id;

					// Load and initialize upload library
					$this->load->library('upload', $config);
					$this->upload->initialize($config);

					// Upload file to server
					if( ! $this->upload->do_upload('file') ){
						$this->output
								->set_output( $this->upload->display_errors('','') )
								->set_status_header(400)
								->_display();
						exit;
					}
					else
					{
						// Uploaded file data
						$file_data = $this->upload->data();
						$upload_data['a_id']          = UUID_TO_BIN($a_id);
						$upload_data['attachment_id'] = UUID_TO_BIN($attachment_id);
						$upload_data['file_name']     = $file_data['client_name'] !== '' ? $file_data['client_name'] : $file_data['orig_name'];
						$upload_data['file_ext']      = $file_data['file_ext'];
						$upload_data['uploaded_on']   = date("Y-m-d H:i:s");

						if( ! empty($upload_data) )
						{
							// Insert files data into the database
							if( $this->deviation_model->save_attachments($upload_data) )
							{
								$this->output
										->set_content_type('application/json', 'utf-8')
										->set_output( json_encode([
											'file_name'     => $upload_data['file_name'],
											'uploaded_on'   => $upload_data['uploaded_on'],
											'attachment_id' => $attachment_id,
											'response'      => 'OK'
										], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) )
										->set_status_header(200)
										->_display();
								exit;
							}
							else
							{
								// @STEP2: Delete file
							}
						}
					}
				}
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}

	// @TODO: Check ACL
	public function download( $a_id )
	{
		$this->VALID_UUIDv4($a_id);
		$attachment = $this->deviation_model->get_attachment( $a_id );
		if( empty($attachment) ) { show_404(); }

		$deviation = $this->deviation_model->deviationRegistredBy($attachment->a_id);
		if( empty($deviation) ) { show_404(); }

		// @TODO: Check department

		$upload_base_path = CI_UPLOAD_PATH . 'deviation';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}

	// @STEP2: Secure delete
	public function delete()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		$attachment_id = $this->input->post('id');
		$attachment    = $this->deviation_model->get_attachment( $attachment_id );
		if( ! empty($attachment) )
		{
			if( $attachment->a_id === $this->input->post('uuid_kvalprak') )
			{
				if( $this->deviation_model->deviationRegistredBy($attachment->a_id) !== NULL)
				{
					$upload_base_path = CI_UPLOAD_PATH . 'deviation';
					$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
					$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

					if( file_exists($upload_file) )
					{
						if( $this->deviation_model->delete_attachments( $attachment->attachment_id ) )
						{
							if( unlink($upload_file) )
							{
								$this->output
										->set_status_header(200)
										->set_content_type('application/json', 'utf-8')
										->set_output(json_encode(['result' => TRUE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
										->_display();
								exit;
							}
						}
					}
				}
			}
		}

		$this->output
				->set_status_header(404)
				->set_content_type('application/json', 'utf-8')
				->set_output(json_encode(['result' => FALSE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
				->_display();
		exit;
	}

	public function emails()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		$emailId     = $this->input->post('emailId');
		$departments = $this->input->post('departments');

		if( empty($emailId) OR empty($departments) ) {
			$this->output
					->set_status_header(404)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['result' => FALSE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$users  = $this->deviation_model->get_email_default( $emailId, $departments );
		$emails = [];
		if( ! empty($users) )
		{
			foreach($users as $user)
			{
				if( ! isset($this->users[$user]) )
					continue;
				$emails[] = $this->users[$user]->name;
			}
		}

		if( ! empty($emails) )
		{
			$this->output
					->set_status_header(200)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode($emails, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		exit;
	}

	public function connect( $id )
	{
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($id);
		if( empty($this->data['deviation']['registred']) ) { show_404(); }

		if( $this->input->method(TRUE) === 'POST' )
		{
			$eventanalysis = array();
			if(isset($_POST['eventanalysis'])) {
				foreach($_POST['eventanalysis'] AS $value) {
					$eventanalysis[] = array(
						'a_id'			=> UUID_TO_BIN($id),
						'ea_id'			=> UUID_TO_BIN($value)
					);
				}
			}

			// Spara
			$this->deviationlib->insertDeviationEventAnalysisMap($eventanalysis,$id);

			$riskassesments = array();
			if(isset($_POST['riskassesments'])) {
				foreach($_POST['riskassesments'] AS $value) {
					$riskassesments[] = array(
						'a_id'			=> UUID_TO_BIN($id),
						'ra_id'			=> UUID_TO_BIN($value)
					);
				}
			}

			// Spara
			$this->deviationlib->insertDeviationRiskAssesmentsMap($riskassesments,$id);

			redirect('deviation/view/' . $id); exit;
		}

		$this->data['deviationFields'] = array(
			'eventanalysis' => array(
				'type' => 'select-multiple',
				'id' => 'eventanalysis',
				'name' => 'eventanalysis[]',
				'class' => 'required',
				'values' => array('' => 'Välj händelseanalys'),
				'placeholder' => 'Välj händelseanalys',
				'prepend' => '<label>Anslut händelseanalyser</label>'
			),
			'riskassesments' => array(
				'type' => 'select-multiple',
				'id' => 'riskassesments',
				'name' => 'riskassesments[]',
				'class' => 'required',
				'values' => array('' => 'Välj riskbedömning'),
				'placeholder' => 'Välj riskbedömning',
				'prepend' => '<label>Anslut riskbedömningar</label>'
			)
		);

		$this->data['deviation']['fields'] = $this->deviation_model->getDeviationPageAndOrId($id,$this->data['rights']['update']);
		if( empty($this->data['deviation']['fields']) ) { show_404(); }
		$this->data['getDeviationEventAnalysisMap']  = $this->deviationlib->getDeviationEventAnalysisMap($id);
		$this->data['getDeviationRiskAssesmentsMap'] = $this->deviationlib->getDeviationRiskAssesmentsMap($id);
		$this->data['getEventAnalysisNames']         = $this->deviationlib->getEventAnalysisNames($this->auth_company_id);
		$this->data['getRiskAssessmentsNames']       = $this->deviationlib->getRiskAssessmentsNames($this->auth_company_id);

		$this->data['deviationFields']['eventanalysis']['values']         += $this->data['getEventAnalysisNames'];
		$this->data['deviationFields']['eventanalysis']['multiple-value']  = $this->data['getDeviationEventAnalysisMap'];
		$this->data['deviationFields']['riskassesments']['values']        += $this->data['getRiskAssessmentsNames'];
		$this->data['deviationFields']['riskassesments']['multiple-value'] = $this->data['getDeviationRiskAssesmentsMap'];

		$this->data['a_id'] = $id;
		$this->load->view('general/deviation/connect',$this->data);
	}

	private function _loadFields()
	{
		$this->load->model('group_model');
		$this->data['fields']  = $this->deviation_model->getDeviationFieldsByPage(array(1,2,3));
		$this->data['groups']  = $this->group_model->get_all();
		$this->data['options'] = $this->deviation_model->getDropdown();
		$this->data['rights']  = $this->acl['deviation'];

		$this->_setOptions($this->data['fields'], $this->data['options']);		
	}

	private function _search()
	{
		$this->_loadFields();

		if( ! empty($this->data['rights']['read']) && ! empty($this->data['rights']['create']) )
		{
			// @STEP2: If ['rights']['read'] are empty, get ['rights']['create'] instead. And only show user created content.
			$deviations = $this->deviation_model->search( $this->data['rights']['read'] );

			$this->data['search']     = $deviations['search'];
			$this->data['list']       = $deviations['list'];
			$this->data['post']       = $deviations['post'];
			$this->data['deviations'] = $deviations['deviations'];
			$this->data['results']    = $deviations['results'];
		}

		if( ! empty($this->data['rights']['read']) && ! empty($this->data['search']) )
		{
			$this->data['sidebar']['active'] = TRUE;
			$this->data['sidebar']['bright'] = TRUE;
			$this->data['sidebar']['html']   = $this->load->view('general/deviation/search', $this->data, TRUE);
		}

		$this->data['newDeviation'] = $this->deviation_model->newDeviation();
		$this->load->view('general/deviation/display',$this->data);
	}

	private function _export()
	{
		$this->_loadFields();

		if(!CI_DEVIATION_EXPORT)
			show_404();

		if(!is_role('Systemadministratör'))
			show_404();

		if(empty($this->data['rights']['read']) && empty($this->data['rights']['create']))
			show_404();

		header('Content-type: text/csv');
		header(sprintf('Content-Disposition: attachment; filename="Avvikelser %s.csv"', date('Y-m-d')));
		header('Expires: 0');
		header('Content-Transfer-Encoding: binary');
		header('Cache-Control: private, no-transform, no-store, must-revalidate');

		$file = fopen('php://output', 'w');

		$this->deviation_model->write_export_deviation($file, $this->acl['deviation']['read']);

		fclose($file);
	}

	public function resolve($id = NULL) {
		$this->VALID_UUIDv4($id);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->deviation_model->change_state($id, 'resolved');
		redirect('deviation#active');
	}

	public function resolve_archived($id = NULL) {
		$this->VALID_UUIDv4($id);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->deviation_model->change_state($id, 'resolved');
		redirect('deviation#archived');
	}

	public function mark_incomplete($id = NULL) {
		$this->VALID_UUIDv4($id);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->deviation_model->mark_incomplete($id);
		redirect('deviation#active');
	}

	public function archive($id = NULL) {
		$this->VALID_UUIDv4($id);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->deviation_model->change_state($id, 'archived');
		redirect('deviation#resolved');
	}

	public function activate($id = NULL) {
		$this->VALID_UUIDv4($id);

		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }

		$this->deviation_model->change_state($id, 'active');
		redirect('deviation#resolved');
	}

	public function display()
	{
		if(
			CI_DEVIATION_EXPORT &&
			isset($_POST['export']) &&
			is_role('Systemadministratör')
		)
		{
			return $this->_export();
		}
		else
		{
			return $this->_search();
		}
	}

	private function _setSelected($fields, &$selected, $departments, $id)
	{
		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				$selected[$department] = $departments;
			}
		}

		$emails = $this->deviation_model->get_email($id);

		if( ! empty($this->data['fields']['type']['email']) && ! empty($emails) )
		{
			foreach($this->data['fields']['type']['email'] as $email)
			{
				if( isset($emails[$email]) )
				{
					foreach($emails[$email] as $user_id)
					{
						if( ! isset($this->users[$user_id]) )
							continue;

						$selected[$email][] = $this->users[$user_id]->name;
					}
				}
			}
		}

	}

	/**
	 * Track share action for deviations
	 */
	public function track_share()
	{
		// Allow both AJAX and regular POST requests
		if( $this->input->method(TRUE) !== 'POST' )
		{
			log_message('error', 'Track deviation share: Not a POST request');
			exit;
		}

		// Check if user is logged in
		if( ! $this->auth_user_id )
		{
			log_message('error', 'Track deviation share: User not logged in');
			$this->output
					->set_status_header(401)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Not authenticated'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$deviation_id = $this->input->post('deviation_id');
		$share_url = $this->input->post('share_url');

		// Log for debugging
		log_message('debug', 'Track deviation share called with deviation_id: ' . $deviation_id . ', share_url: ' . $share_url . ', user_id: ' . $this->auth_user_id);

		if( empty($deviation_id) || empty($share_url) )
		{
			log_message('error', 'Track deviation share: Missing required parameters');
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Missing parameters'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		try {
			$this->VALID_UUIDv4($deviation_id);
		} catch (Exception $e) {
			log_message('error', 'Track deviation share: Invalid UUID - ' . $deviation_id);
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Invalid ID'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Check if deviation exists
		$this->load->model('deviation_model');
		if( ! $this->deviation_model->get_deviation_exists($deviation_id) )
		{
			log_message('error', 'Track deviation share: Deviation not found - ' . $deviation_id);
			$this->output
					->set_status_header(404)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Deviation not found'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Load share model
		$this->load->model('share_model');
		log_message('debug', 'Share model loaded successfully for deviation');

		// Check if deviation is already shared
		$is_shared = $this->share_model->is_entity_shared('deviation', $deviation_id);
		log_message('debug', 'Deviation shared status: ' . ($is_shared ? 'true' : 'false'));

		if( ! $is_shared )
		{
			// Add new share record
			log_message('debug', 'Adding new deviation share record');
			$share_id = $this->share_model->add_share('deviation', $deviation_id, NULL, $this->auth_user_id);

			if( $share_id )
			{
				log_message('debug', 'Deviation share added successfully with ID: ' . $share_id);
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['success' => TRUE, 'share_id' => $share_id], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
			else
			{
				log_message('error', 'Failed to add deviation share record');
			}
		}
		else
		{
			// Deviation already shared - track this share action
			log_message('debug', 'Updating existing deviation share tracking');
			$updated = $this->share_model->update_share_date('deviation', $deviation_id);

			if( $updated )
			{
				log_message('debug', 'Deviation share tracking updated successfully');
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['success' => TRUE, 'message' => 'Deviation share tracked'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
			else
			{
				log_message('error', 'Failed to update deviation share tracking');
			}
		}

		$this->output
				->set_status_header(500)
				->set_content_type('application/json', 'utf-8')
				->set_output(json_encode(['success' => FALSE, 'message' => 'Deviation share failed'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
				->_display();
		exit;
	}

}
