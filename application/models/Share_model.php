<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Share_model extends MY_Model
{
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Add a new shared entity
	 */
	public function add_share($entity_type, $entity_id, $shared_with = NULL, $shared_by = NULL)
	{
		if (empty($shared_by)) {
			$shared_by = config_item('auth_user_id');
		}
		
		if (empty($shared_by)) {
			log_message('error', 'No authenticated user found for sharing');
			return FALSE;
		}
		
		$share_id = UUIDv4();
		$data = array(
			'id'          => UUID_TO_BIN($share_id),
			'entity_type' => $entity_type,
			'entity_id'   => UUID_TO_BIN($entity_id),
			'shared_by'   => UUID_TO_BIN($shared_by),
			'shared_with' => $shared_with,
			'status'      => 'active'
		);

		log_message('debug', 'Attempting to insert share data for entity: ' . $entity_type . ' - ' . $entity_id);
		log_message('debug', 'Shared by user: ' . $shared_by);
		log_message('debug', 'Table name: ' . $this->db_table('shares'));

		if ($this->db->insert($this->db_table('shares'), $data)) {
			log_message('debug', 'Share inserted successfully with ID: ' . $share_id);
			return $share_id;
		}

		log_message('error', 'Failed to insert share: ' . $this->db->last_query());
		log_message('error', 'Database error: ' . json_encode($this->db->error()));
		return FALSE;
	}

	/**
	 * Get all shared documents
	 */
	public function get_all_shared_documents($limit = NULL, $offset = NULL, $search = NULL)
	{
		// First get the basic share information without joins that might cause duplicates
		$this->db->select('shares.id as share_id, shares.entity_id, shares.shared_by,
			shares.created_at as shared_date, shares.shared_with,
			documents.name as document_name, documents.description as document_description,
			documents.document_type, documents.last_revised, documents.valid_until,
			folders.name as folder_name, menus.name as menu_name, users.name as shared_by_name');

		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');

		$this->db->where('shares.entity_type', 'document');
		$this->db->where('shares.status', 'active');
		$this->db->where('documents.status', 'published');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		$this->db->order_by('shares.created_at', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();

		if ($query->num_rows() > 0) {
			$results = $query->result();

			// Process results and get file extensions separately to avoid duplicates
			foreach ($results as $result) {
				$result->share_id = BIN_TO_UUID($result->share_id);
				$result->document_id = BIN_TO_UUID($result->entity_id);
				$result->entity_id = BIN_TO_UUID($result->entity_id);
				$result->shared_by = BIN_TO_UUID($result->shared_by);

				// Get file extension separately
				$this->db->select('file_ext');
				$this->db->from($this->db_table('document_attachment'));
				$this->db->where('document_id', UUID_TO_BIN($result->document_id));
				$this->db->limit(1);
				$ext_query = $this->db->get();

				$result->file_ext = '';
				if ($ext_query->num_rows() > 0) {
					$result->file_ext = $ext_query->row()->file_ext;
				}
			}

			return $results;
		}

		return array();
	}

	/**
	 * Get count of shared documents
	 */
	public function get_shared_documents_count($search = NULL)
	{
		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		
		$this->db->where('shares.entity_type', 'document');
		$this->db->where('documents.status', 'published');

		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		return $this->db->count_all_results();
	}

	/**
	 * Check if entity is already shared
	 */
	public function is_entity_shared($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$query = $this->db->get($this->db_table('shares'));
		
		return $query->num_rows() > 0;
	}

	/**
	 * Get share information for an entity
	 */
	public function get_entity_share($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$query = $this->db->get($this->db_table('shares'));

		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}

		return NULL;
	}

	/**
	 * Get share by share_id
	 */
	public function get_share_by_id($share_id)
	{
		$this->db->where('id', UUID_TO_BIN($share_id));
		$query = $this->db->get($this->db_table('shares'));

		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}

		return NULL;
	}

	/**
	 * Update share tracking when entity is shared again
	 */
	public function update_share_date($entity_type, $entity_id)
	{
		// For the current table structure, we don't have an updated_at column
		// This method maintains backward compatibility but doesn't actually update anything
		// since shares are tracked by created_at timestamp only
		
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$query = $this->db->get($this->db_table('shares'));
		
		return $query->num_rows() > 0; // Return true if share exists
	}

	/**
	 * Remove share by entity
	 */
	public function remove_share($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		return $this->db->delete($this->db_table('shares'));
	}

	/**
	 * Remove share by share_id
	 */
	public function remove_share_by_id($share_id)
	{
		$this->db->where('id', UUID_TO_BIN($share_id));
		return $this->db->delete($this->db_table('shares'));
	}

	/**
	 * Get sharing statistics for all entities
	 */
	public function get_sharing_stats()
	{
		// Total shared entities
		$this->db->where('status', 'active');
		$total_shared = $this->db->count_all_results($this->db_table('shares'));

		// Entities shared this month
		$this->db->where('status', 'active');
		$this->db->where('created_at >=', date('Y-m-01 00:00:00'));
		$this->db->where('created_at <=', date('Y-m-t 23:59:59'));
		$this_month = $this->db->count_all_results($this->db_table('shares'));

		// Entities shared this week
		$this->db->where('status', 'active');
		$this->db->where('created_at >=', date('Y-m-d 00:00:00', strtotime('monday this week')));
		$this->db->where('created_at <=', date('Y-m-d 23:59:59', strtotime('sunday this week')));
		$this_week = $this->db->count_all_results($this->db_table('shares'));

		// Stats by entity type
		$by_type = array();
		$types = ['document', 'deviation', 'eventanalysis', 'riskassessment'];
		foreach ($types as $type) {
			$this->db->where('entity_type', $type);
			$this->db->where('status', 'active');
			$by_type[$type] = $this->db->count_all_results($this->db_table('shares'));
		}

		return array(
			'total_shared' => $total_shared,
			'this_month' => $this_month,
			'this_week' => $this_week,
			'by_type' => $by_type
		);
	}

	/**
	 * Backward compatibility methods
	 */
	public function is_document_shared($document_id)
	{
		return $this->is_entity_shared('document', $document_id);
	}

	public function get_document_share($document_id)
	{
		return $this->get_entity_share('document', $document_id);
	}

	/**
	 * Get all shared entities (documents, deviations, event analysis, risk assessments)
	 */
	public function get_all_shared_entities($limit = NULL, $offset = NULL, $search = NULL)
	{
		// Use DISTINCT to prevent duplicates from the start
		$this->db->distinct();
		$this->db->select('shares.id as share_id, shares.entity_type, shares.entity_id, shares.shared_by,
			shares.created_at as shared_date, shares.shared_with, shares.status,
			users.name as shared_by_name');

		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');

		$this->db->where('shares.status', 'active');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('shares.entity_type', $search);
			$this->db->or_like('users.name', $search);
			$this->db->group_end();
		}

		$this->db->order_by('shares.created_at', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();

		log_message('debug', 'Share query: ' . $this->db->last_query());

		if ($query->num_rows() > 0) {
			$results = $query->result();
			log_message('debug', 'Found ' . count($results) . ' shared entities');

			// Remove any potential duplicates based on share_id
			$unique_results = array();
			$seen_ids = array();

			foreach ($results as $result) {
				$share_id_binary = $result->share_id;
				$share_id_string = BIN_TO_UUID($share_id_binary);

				if (!in_array($share_id_string, $seen_ids)) {
					$result->share_id = $share_id_string;
					$result->entity_id = BIN_TO_UUID($result->entity_id);
					$result->shared_by = BIN_TO_UUID($result->shared_by);
					$unique_results[] = $result;
					$seen_ids[] = $share_id_string;
				}
			}

			log_message('debug', 'After deduplication: ' . count($unique_results) . ' unique shared entities');
			return $unique_results;
		}

		return array();
	}

	/**
	 * Get count of all shared entities
	 */
	public function get_shared_entities_count($search = NULL)
	{
		$this->db->distinct();
		$this->db->select('shares.id');
		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');

		$this->db->where('shares.status', 'active');

		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('shares.entity_type', $search);
			$this->db->or_like('users.name', $search);
			$this->db->group_end();
		}

		$query = $this->db->get();
		return $query->num_rows();
	}
}
