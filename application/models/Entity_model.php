<?php
class Entity_model extends MY_Model {
	
	/**
	 * Check if entity exists by table and ID
	 */
	public function entity_exists($table_name, $entity_id, $id_column = 'id')
	{
		$this->db->select('1');
		$this->db->from($this->db_table($table_name));
		$this->db->where($id_column, UUID_TO_BIN($entity_id));
		$this->db->limit(1);
		
		$query = $this->db->get();
		return $query->num_rows() > 0;
	}
	
	/**
	 * Check if eventanalysis exists
	 */
	public function get_eventanalysis_exists($ea_id)
	{
		return $this->entity_exists('eventanalysis', $ea_id);
	}
	
	/**
	 * Check if risk assessment exists
	 */
	public function get_riskassessment_exists($ra_id)
	{
		return $this->entity_exists('riskassessments', $ra_id, 'ra_id');
	}
	
	/**
	 * Check if deviation exists
	 */
	public function get_deviation_exists($dev_id)
	{
		return $this->entity_exists('deviation', $dev_id, 'dev_id');
	}
	
	/**
	 * Check if document exists
	 */
	public function get_document_exists($doc_id)
	{
		return $this->entity_exists('documents', $doc_id, 'doc_id');
	}
}
