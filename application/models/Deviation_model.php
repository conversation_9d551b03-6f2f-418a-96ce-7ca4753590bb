<?php
class Deviation_model extends MY_Model {

	public function _get_contacts()
	{
		return $this->input->post('contacts[]');
	}

	/**
	 * Check if deviation exists
	 */
	public function get_deviation_exists($a_id)
	{
		$this->db->select('1');
		$this->db->from('deviation_fields_active');
		$this->db->where('a_id', UUID_TO_BIN($a_id));
		$this->db->limit(1);
		
		$query = $this->db->get();
		return $query->num_rows() > 0;
	}

	function getDeviationName($a_id = NULL) {
		$binding   = array();
		$sql = "SELECT
					da.answer
				FROM
					deviation_fields_active AS dfa
				INNER JOIN
					deviation_fields AS df
						ON df.df_id=dfa.df_id
				LEFT JOIN
					deviation_answers AS da
						ON da.a_id=dfa.a_id AND da.df_id=dfa.df_id
				WHERE
					df.input = 'input' and df.required_kvalprak = 1 and
					dfa.a_id = ?";
		$binding[] = UUID_TO_BIN($a_id);
		$q = $this->db->query($sql,$binding);
		$deviation = array();
		return $q->result()[0]->answer;
	}

	function getDeviationPageAndOrId($a_id = NULL, $departments = NULL, $page = NULL) {
		$deviation = array();
		$binding   = array();
		$bin_departments = array();

		if( ! empty($departments) ) {
			foreach($departments as $department) {
				$bin_departments[] = UUID_TO_BIN($department);
			}
			$binding[] = $bin_departments;
		}
		if( ! empty($a_id) )
			$binding[] = UUID_TO_BIN($a_id);
		if( ! empty($page) )
			$binding[] = $page;

		$sql = "SELECT
					dfa.a_id,
					df.df_id,
					df.input,
					df.search,
					df.list,
					df.title,
					df.required,
					df.required_kvalprak,
					df.description,
					df.page,
					da.answer
				FROM
					deviation_fields_active AS dfa
				INNER JOIN
					deviation_fields AS df
						ON df.df_id=dfa.df_id
				LEFT JOIN
					deviation_answers AS da
						ON da.a_id=dfa.a_id AND da.df_id=dfa.df_id";
		if( ! empty($departments) ) {
			$sql .=	"
				INNER JOIN
					deviation_department AS dd ON dd.a_id=dfa.a_id
					AND dd.de_id IN ? ";
		}
			$sql .= "
				WHERE
					dfa.a_id = ?";
		if( $page ) {
			$sql .=	" AND df.page = ?";
		}
		$sql .=	   "
				GROUP BY
					df.df_id
				ORDER BY
					dfa.a_id DESC,
					df.page ASC,
					df.order ASC,
					df.title ASC";

		$q = $this->db->query($sql,$binding);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$row->a_id  = BIN_TO_UUID($row->a_id);
				$row->df_id = BIN_TO_UUID($row->df_id);

				$deviation[$row->df_id] = $row;
			}
		}

		return $deviation;
	}

	function deviationDepartment( $a_id = NULL)
	{

		$binding = [];
		$binding[] = UUID_TO_BIN($a_id);

		$departments = [];
		$sql = "SELECT
					`de_id`
				FROM
					`deviation_department`";
		if( !empty($a_id) )
			$sql = $sql . "WHERE
					`a_id` = ?";

		$q = $this->db->query($sql,$binding);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$departments[] = BIN_TO_UUID($row->de_id);
			}
		}

		return $departments;
	}

	function deviationNextPage($deviation_two, $deviation_three)
	{
		// var_dump($departments_id);exit;
		if( empty($deviation_two) && empty($deviation_three) ) { return []; }

		$bindings   = [];
		$de_two     = [];
		$de_three   = [];
		$deviations = [];
		if( ! empty($deviation_two) )
		{
			foreach($deviation_two as $dep):
				$de_two[] = UUID_TO_BIN($dep);
			endforeach;
		}

		if( ! empty($deviation_three) )
		{
			foreach($deviation_three as $dep):
				$de_three[] = UUID_TO_BIN($dep);
			endforeach;
		}

		$query ="SELECT
					d.regby_two, d.regby_three, a.a_id, a.answer as title
				FROM
					deviation AS d
				LEFT JOIN
					deviation_department AS de
						ON de.a_id=d.a_id
				LEFT JOIN
					deviation_answers AS a
						ON a.a_id=d.a_id
				WHERE
					a.df_id = ? AND
					d.company_id = ? AND ";

		if( ! empty($deviation_two) && ! empty($deviation_three) )
		{
			$query .= '(';
		}

		if( ! empty($deviation_two) )
		{
			$query .="(d.regby_two IS NULL AND de.de_id IN ?)";
		}

		if( ! empty($deviation_two) && ! empty($deviation_three) )
		{
			$query .= ' OR ';
		}

		if( ! empty($deviation_three) )
		{
			$query .="(d.regby_three IS NULL AND de.de_id IN ?)";
		}

		if( ! empty($deviation_two) && ! empty($deviation_three) )
		{
			$query .= ')';
		}

		$query .= "
				GROUP BY
					d.a_id
				ORDER BY
					d.reg_date_two DESC,
					d.reg_date_one DESC";

		$bindings[] = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
		$bindings[] = UUID_TO_BIN($this->auth_company_id);
		if( ! empty($deviation_two) )
			$bindings[] = $de_two;
		if( ! empty($deviation_three) )
			$bindings[] = $de_three;

		$q = $this->db->query($query, $bindings);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $deviation)
			{
				// var_dump($deviation);exit;
				$deviation->a_id = BIN_TO_UUID($deviation->a_id);
				$deviation->regby_two = BIN_TO_UUID($deviation->regby_two);
				$deviation->regby_three = BIN_TO_UUID($deviation->regby_three);
				$deviations[] = $deviation;
			}
		}

		return $deviations;
	}

	function get_company_id() {
		$q = $this->db->select( 'company_id' )->from( $this->db_table('user_table'))->get()->row();
		return BIN_TO_UUID($q->company_id);
	}

	function deviationNextPageById($id) {
		$sql = "SELECT
					regby_one,
					regby_two,
					regby_three,
					eventanalysis
				FROM
					deviation
				WHERE
					a_id = ? AND
					company_id = ?";

		$q = $this->db->query($sql,[
			UUID_TO_BIN($id),
			UUID_TO_BIN($this->auth_company_id)
		]);

		if( $q->num_rows() === 1 )
		{
			$row = $q->row();
			$row->regby_one  = BIN_TO_UUID($row->regby_one);
			$row->regby_two  = BIN_TO_UUID($row->regby_two);
			$row->regby_three = BIN_TO_UUID($row->regby_three);
			if (isset($row->eventanalysis))
				$row->eventanalysis = BIN_TO_UUID($row->eventanalysis);
			return $row;
		}

		return NULL;
	}

	function deviationRegistredBy($id) {
		return $this->deviationNextPageById($id);
	}

	function getDropdown($df_id = NULL) {
		if( $df_id )
		{
			$sql = "SELECT
						`dd_id`,
						`df_id`,
						`title`,
						`default`
					FROM
						deviation_dropdown
					WHERE
						`df_id` = ?
					ORDER BY
						`order` ASC,
						`title` ASC";
			$q = $this->db->query($sql,[
				UUID_TO_BIN($df_id)
			]);
		}
		else
		{
			$sql = "SELECT
						`dd_id`,
						`df_id`,
						`title`,
						`default`
					FROM
						deviation_dropdown
					ORDER BY
						`order` ASC,
						`title` ASC";
			$q = $this->db->query($sql);
		}

		$dropdowns = array();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$row->dd_id = BIN_TO_UUID($row->dd_id);
				$row->df_id = BIN_TO_UUID($row->df_id);

				$dropdown = new stdClass();
				$dropdown->option_id = $row->dd_id;
				$dropdown->name      = $row->title;
				$dropdown->default   = $row->default;
				// $dropdown['dropdown'][$row->dd_id] = $row->title;
				if( $df_id )
				{
					$dropdowns[$row->dd_id] = $dropdown;
				}
				else
				{
					$dropdowns[$row->df_id][$row->dd_id] = $dropdown;
				}
			}
		}

		return $dropdowns;
	}

	function getDeviationFieldsByPage($page,$byPage = FALSE) {
		$page   = (array) $page;
		$fields = array();

		$sql = "SELECT
					`df_id`,
					`input`,
					`required`,
					`required_kvalprak`,
					`page`,
					`order`,
					`title`,
					`description`
				FROM
					`deviation_fields`
				WHERE
					`page` IN ?
				ORDER BY
					`page` ASC,
					`order` ASC,
					`title` ASC
					";
		$q = $this->db->query($sql, array($page));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $field)
			{
				$field->df_id = BIN_TO_UUID($field->df_id);
				if( $byPage )
				{
					$fields[$field->page][] = $field;
				}
				else
				{
					$fields['all'][$field->df_id] = $field;
					$fields['type'][$field->input][$field->df_id] = $field->df_id;
				}

			}
		}

		return $fields;
	}

	public function newDeviation() {
		$q = $this->db
				->select('page,required_kvalprak')
				->get($this->db_table('deviation_fields'));

		$fields = [
			0 => 0,
			1 => 0,
			2 => 0,
			3 => 0,
		];

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $field)
			{
				if( $field->page == 0 && $field->required_kvalprak == 1 )
				{
					return FALSE;
				}

				$fields[$field->page] += 1;
			}
		}

		if(	$fields[1] === 0 OR $fields[2] === 0 OR $fields[3] === 0 )
		{
			return FALSE;
		}

		return TRUE;
	}

	public function get_attachments( $a_id )
	{
		$attachments = NULL;
		$a_id = UUID_TO_BIN($a_id);

		$q = $this->db
				->select('attachment_id,uploaded_on,file_name,file_ext')
				->where('a_id',$a_id)
				->order_by('uploaded_on','ASC')
				->get($this->db_table('deviation_attachment'));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $attachment)
			{
				$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
				$attachments[] = $attachment;
			}
		}

		return $attachments;
	}

	public function get_attachment( $attachment_id )
	{
		$attachment = array();
		$attachment_id = UUID_TO_BIN($attachment_id);

		$q = $this->db
				->select('a_id,attachment_id,uploaded_on,file_name,file_ext')
				->where('attachment_id',$attachment_id)
				->limit(1)
				->get($this->db_table('deviation_attachment'));

		if( $q->num_rows() === 1)
		{
			$attachment = $q->row();
			$attachment->a_id          = BIN_TO_UUID($attachment->a_id);
			$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
		}

		return $attachment;
	}

	public function save( $a_id = NULL, $page = NULL)
	{
		$a_id   = UUID_TO_BIN($a_id);
		$insert = array();
		$update = array();

		switch($page)
		{
			default:
				$insert = array(
					'company_id'   => UUID_TO_BIN($this->auth_company_id),
					'reg_date_one' => date('Y-m-d H:i:s'),
					'regby_one'    => UUID_TO_BIN($this->auth_user_id)
				);
			break;
			case '2':
				$update = array(
					'reg_date_two' => date('Y-m-d H:i:s'),
					'regby_two'    => UUID_TO_BIN($this->auth_user_id)
				);
			break;
			case '3':
				$update = array(
					'reg_date_three' => date('Y-m-d H:i:s'),
					'regby_three'    => UUID_TO_BIN($this->auth_user_id)
				);
			break;
		}

		if( !empty($insert) )
		{
			$insert['a_id'] = $a_id;
			$q = $this->db->insert($this->db_table('deviation'), $insert);
		}

		if( $eventanalysis = $this->input->post('eventanalysis') )
		{
			$df_id = NULL;
			$user_id = NULL;

			if( strlen($eventanalysis) === 73 )
			{
				list($df_id,$user_id) = explode('_',$eventanalysis);
				if(
					strlen($df_id) === 36 && VALID_UUIDv4($df_id) &&
					strlen($user_id) === 36 && VALID_UUIDv4($user_id)
				)
				{
					$df_id = UUID_TO_BIN($df_id);
					$update['eventanalysis'] = UUID_TO_BIN($user_id);
				}
			}
			// Fallback
			elseif( strlen($eventanalysis) === 36 && VALID_UUIDv4($eventanalysis) )
			{
				$update['eventanalysis'] = UUID_TO_BIN($eventanalysis);
			}

			if( empty($update['eventanalysis']) ) {
				$update['eventanalysis'] = UUID_TO_BIN($this->auth_user_id);
			}
			// @TODO: Insert after step 3 have been done instead.
			$q = $this->db->insert($this->db_table('user_messages'),[
				'user_id'    => $update['eventanalysis'],
				'type'       => 'eventanalysis',
				'type_id'    => $a_id,
				'action'     => 'create',
				'severity'   => 'critical',
				'created_at' => date('Y-m-d H:i:s'),
				'comment'    => NULL
			]);

			if( $df_id !== NULL ) {
				$q = $this->db->insert($this->db_table('deviation_answers'),[
					'a_id'   => $a_id,
					'df_id'  => $df_id,
					'answer' => $user_id
				]);
			}
		}

		if( !empty($update) )
		{
			$q = $this->db
				->where('a_id',$a_id)
				->update($this->db_table('deviation'), $update);
		}

		return $q;
	}

	public function mark_incomplete($id) {
		$a_id   = UUID_TO_BIN($id);
		$this->db
				->where('a_id',$a_id)
				->update($this->db_table('deviation'), array("reg_date_three" => null));
	}

	public function change_state($id, $status)
	{
		$a_id   = UUID_TO_BIN($id);
		$this->db
				->where('a_id',$a_id)
				->update($this->db_table('deviation'), array("status" => $status));
		if ($status == 'resolved') {
			$this->db
				->where('a_id',$a_id)
				->update($this->db_table('deviation'), array("resolved_date" => date('Y-m-d H:i:s')));
		} else if ($status == 'active') {
			$this->db
				->where('a_id',$a_id)
				->update($this->db_table('deviation'), array("resolved_date" => null));
		}
	}

	public function save_answers( $a_id = NULL, $all = array() )
	{
		$data   = array();
		$delete = array();
		$a_id   = UUID_TO_BIN($a_id);

		if( !empty($all) && $this->input->method(TRUE) === 'POST' )
		{
			foreach($all as $id => $field)
			{
				if($field->input !== 'eventanalysis')
				{
					$post = $this->input->post('deviation_' . $id);
					if(is_array($post))
						$post = json_encode($post);

					$dbid = UUID_TO_BIN($id);

					$delete[] = $dbid;

					if( !empty($post) )
					{
						$data[] = array(
							'a_id'   => $a_id,
							'df_id'  => $dbid,
							'answer' => $post
						);
					}
				} else {
					$eventanalysis = $this->input->post('eventanalysis');
					$df_id = NULL;
					$user_id = NULL;
					if (str_contains($eventanalysis, '_')) {
					list($df_id,$user_id) = explode('_',$eventanalysis);
					if(
						strlen($df_id) === 36 && VALID_UUIDv4($df_id) &&
						strlen($user_id) === 36 && VALID_UUIDv4($user_id)
					)
					{
						$df_id = UUID_TO_BIN($df_id);
					}
					}
					$delete[] = $df_id;
					$data[] = array(
						'a_id'   => $a_id,
						'df_id'  => $df_id,
						'answer' => $user_id
					);
				}
			}
		}
		else
		{
			return FALSE;
		}

		$q = $this->db
				->where('a_id',$a_id)
				->where_in('df_id',$delete)
				->delete($this->db_table('deviation_answers'));

		if(!$q)
			return FALSE;

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('deviation_answers'),$data);

		return TRUE;
	}

	public function save_departments( $a_id = NULL )
	{
		// @STEP2: Validate department
		$departments = $this->input->post('department[]');
		if (!isset($departments)) $departments = $this->input->post('department');
		if( $this->input->method(TRUE) === 'POST' && isset($departments) )
		{
			$a_id = UUID_TO_BIN($a_id);
			$data = array();

			if(is_array($departments))
			{
				foreach($departments as $department)
				{
					$data[] = array(
						'a_id'  => $a_id,
						'de_id' => UUID_TO_BIN($department)
					);
				}
				if( ! empty($data) )
				{
					$q = $this->db
							->where('a_id',$a_id)
							->delete($this->db_table('deviation_department'));
					if( $q )
					{
						return $this->db->insert_batch($this->db_table('deviation_department'),$data);
					}
				}
			}
		}

		return NULL;
	}

	public function save_emails( $a_id = NULL, $departments = [] )
	{
		if( empty($a_id) OR empty($departments) ) { return FALSE; }

		if(
			$this->input->method(TRUE) === 'POST' &&
			$emailId = $this->input->post('emailId')
		)
		{
			if( empty($emailId) OR ! VALID_UUIDv4($emailId) ) { return FALSE; }

			$users = $this->get_email_default($emailId, $departments);

			$emails = $this->input->post('emails[]');
			if (!isset($emails)) $emails = $this->input->post('emails');
			if( isset($emails) )
			{
				foreach($emails as $email)
				{
					if( ! in_array($email, $users) )
					{
						$users[] = $email;
					}
				}
			}

			$a_id    = UUID_TO_BIN($a_id);
			$emailId = UUID_TO_BIN($emailId);
			$data    = array();
			$address = array();
			if( ! empty($users) )
			{
				foreach($users as $user)
				{
					if( isset($this->users[$user]) )
					{
						$data[] = array(
							'a_id'    => $a_id,
							'df_id'   => $emailId,
							'user_id' => UUID_TO_BIN($user)
						);

						$address[$this->users[$user]->email] = $this->users[$user]->name;
					}
				}

				if( ! empty($data) )
				{
					if( $this->db->insert_batch($this->db_table('deviation_email'),$data) )
					{
						return $address;
					}
				}
			}
		}

		return FALSE;
	}

	public function save_fields( $a_id = NULL, $all = array() )
	{
		if( !empty($all) && $a_id !== NULL )
		{
			$a_id = UUID_TO_BIN($a_id);
			$data = array();

			foreach($all as $id => $field)
			{
				$data[] = array(
					'a_id'  => $a_id,
					'df_id' => UUID_TO_BIN($id)
				);
			}

			if( ! empty($data) )
				return $this->db->insert_batch($this->db_table('deviation_fields_active'),$data);
		}

		return FALSE;
	}

	private function _getDeviationBySearchFilter( $departments )
	{
		// SQL Bindings
		$binding = [];

		// Search parameters
		$post = [];

		// Deviation ids
		$a_id = [];

		/**
		 * Searched department didn't return any matches.
		 * Use default SQL and default bindings (all departments).
		 */
		$search_department_empty = false;

		//
		// Departments UUID to BIN
		//

		$bin_departments = [];
		if( ! empty($departments) ) {
			foreach($departments as $department) {
				$bin_departments[] = UUID_TO_BIN($department);
			}
		}

		//
		// Default bindings
		//

		$binding[] = $bin_departments;
		$binding[]  = UUID_TO_BIN($this->auth_company_id);

		$binding_default = $binding;

		//
		// Search or export button pressed
		//

		if(isset($_POST['search']) || isset($_POST['export']))
		{
			unset($_POST['search']);
			unset($_POST['export']);

			$search_array    = [];
			$search_binding  = [];
			$temp_search_sql = '';
			$bin_departments = [];

			// @STEP2: Validate selected inputs
			foreach($_POST as $key => $val)
			{
				$type = explode('_',$key);
				if( isset($type[0]) && $type[0] === 'search' )
				{
					if( isset($type[1]) && !empty($val) )
					{
						switch($type[1])
						{
							case 'input':
							case 'text':
							case 'text_wysiwyg':
								$search_array[] = "(da.df_id=? AND da.answer LIKE '%" . $this->db->escape_like_str($val) . "%')";
								$search_binding[] = UUID_TO_BIN($type[2]);
							break;
							case 'dropdown':
							case 'radio':
							case 'users':
								$search_array[] = '(da.df_id=? AND da.answer = ?)';
								$search_binding[] = UUID_TO_BIN($type[2]);
								$search_binding[] = $val;
							break;
							case 'date':
								$first = 'search_date_first_' . $type[3];
								$last  = 'search_date_last_' . $type[3];

								if($type[2] === 'first')
								{
									$temp_search_sql  = '(da.df_id=? AND da.answer >= ?';
									$search_binding[] = UUID_TO_BIN($type[3]);
									$search_binding[] = $val;
									if( ! isset($_POST[$last]) || empty($_POST[$last]) ) {
										$search_array[] = $temp_search_sql . ')';
										$temp_search_sql = '';
									}
								}
								else
								{
									if( isset($_POST[$first]) && ! empty($_POST[$first]) )
									{
										$search_array[]   = $temp_search_sql . ' AND da.answer <= ?)';
										$search_binding[] = $val;
									}
									else
									{
										$search_array[]   = '(da.df_id=? AND da.answer <= ?)';
										$search_binding[] = UUID_TO_BIN($type[3]);
										$search_binding[] = $val;
									}

									$temp_search_sql = '';
								}

							break;
							case 'department':
								$bin_departments[0] = UUID_TO_BIN($val);
								$search_department_empty = true;
							break;
						}

						$df_id = isset($type[3]) ? $type[3] : $type[2];
						if( $type[1] === 'date')
							$post[$df_id][$type[2]] = $val;
						else
							$post[$df_id] = $val;
					}
				}
			}

			// Replace default department
			if( ! empty($bin_departments) )
				$binding[0] = $bin_departments;

			if( ! empty($search_array) )
			{
				$count_sql = "SELECT
							da.a_id,
							count(da.df_id) as count
						FROM
							`deviation_answers` AS da
						INNER JOIN
							(
								SELECT
									DISTINCT a_id
								FROM
									deviation_department
								WHERE
									de_id IN ?
							) AS dd
							ON dd.a_id=da.a_id
						INNER JOIN
							deviation AS dn
							ON dn.a_id=da.a_id 
								AND company_id = ?
						WHERE
							".implode(' OR ',$search_array)."
						GROUP BY
							da.a_id
						ORDER BY
							count ASC,
							da.a_id DESC";

				$search_binding = array_merge($binding, $search_binding);

				$q = $this->db->query($count_sql, $search_binding);

				$a_id = [];
				if( $q->num_rows() !== 0 )
				{
					foreach($q->result() as $count)
					{
						$a_id[$count->count][] = $count->a_id;
					}
				}

				// Are there a complete match?
				if( isset( $a_id[count($search_array)] ) ) {
					$a_id = array_pop($a_id);
				} else {
					$a_id = null;
				}
			}
		}

		//
		// Return
		//

		return [
			'binding' => [
				'default' => $binding_default,
				'current' => $binding,
			],
			'search' => [
				'department' => [
					'empty' => $search_department_empty,
				],
			],
			'post' => $post,
			'a_id' => $a_id,
		];
	}

	public function export( $departments )
	{
		$data = $this->_getDeviationBySearchFilter($departments);

		$binding    = $data['binding']['current'];
		$deviations = [];
		$a_id       = $data['a_id'];

		// Export all deviations
		if(is_array($a_id) && empty($a_id))
		{
			$sql = "SELECT
					dfa.a_id
				FROM
					deviation_fields_active AS dfa
				INNER JOIN
					deviation_fields AS df
						ON df.df_id=dfa.df_id AND df.page != 0
				INNER JOIN
					deviation_department AS dd
						ON dd.a_id=dfa.a_id
							AND dd.de_id IN ?
				INNER JOIN
					deviation AS dn
						ON dn.a_id=dfa.a_id AND
							-- regby_two IS NOT NULL AND regby_three IS NOT NULL AND
							company_id = ?
				GROUP BY
					dfa.a_id";

			$q = $this->db->query($sql, $binding);

			if( $q->num_rows() !== 0 )
			{
				$a_id = [];

				foreach($q->result() as $deviation)
				{
					$a_id[] = BIN_TO_UUID($deviation->a_id);
				}
			}
		}

		// Look for specific deviations
		if(!empty($a_id))
		{
			$data = [
				'department' => [],
			];

			//
			// Departments
			//

			$sql = "SELECT * FROM `deviation_department`";
			$q = $this->db->query($sql);
			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $department)
				{
				if (in_array(BIN_TO_UUID($department->a_id), $a_id)) {
					$department->a_id  = BIN_TO_UUID($department->a_id);
					$department->de_id = BIN_TO_UUID($department->de_id);

					$data['department'][$department->a_id][] = $department->de_id;
				}
				}
			}

			//
			// Deviations
			//

			// $binding = array_merge($binding, [$a_id]);

			$sql = "SELECT
						dfa.a_id,
						df.df_id,
						df.input,
						dn.status as deviation_phase,
						reg_date_two,
						reg_date_three,
						da.answer
					FROM
						deviation_fields_active AS dfa
					INNER JOIN
						deviation_fields AS df
							ON
								df.df_id=dfa.df_id AND
								df.page != 0 AND
								df.input != 'email' AND
								df.input != 'upload' AND
								df.input != 'eventanalysis'
					LEFT JOIN
						deviation_answers AS da
							ON da.a_id=dfa.a_id AND da.df_id=dfa.df_id
					INNER JOIN
						deviation_department AS dd ON dd.a_id=dfa.a_id
						AND dd.de_id IN ?
					INNER JOIN
						deviation AS dn
							ON dn.a_id=dfa.a_id AND
								-- regby_two IS NOT NULL AND regby_three IS NOT NULL AND 
								company_id = ?
					GROUP BY
						dfa.a_id,
						df.df_id
					ORDER BY
						dn.reg_date_three DESC,
						df.page ASC,
						df.order ASC,
						df.title ASC";

			$q = $this->db->query($sql, $binding);

			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $deviation)
				{
				if (in_array(BIN_TO_UUID($deviation->a_id), $a_id)) {
					$deviation->df_id = BIN_TO_UUID($deviation->df_id);
					$deviation->a_id  = BIN_TO_UUID($deviation->a_id);

					if(
						$deviation->input === 'department' &&
						isset($data['department'][$deviation->a_id])
					)
					{
						$deviation->answer = $data['department'][$deviation->a_id];
					}
					else
					{
						$answer = $deviation->answer;
						$answer = isset($answer) ? trim(strip_tags($answer)) : '';
						$answer = str_replace('&nbsp;', ' ', $answer);

						if(in_array($deviation->input, ['input', 'text', 'text_wysiwyg']))
						{
							$answer = str_replace(["\r", "\n"], '', $answer);

							if(strlen($answer) > 50)
							{
								$answer = trim(mb_substr($answer, 0, 50)) . '...';
							}
						}

						$deviation->answer = $answer;
					}

					$deviations[$deviation->a_id][$deviation->df_id] = $deviation;
				}
				}
			}
		}

		return $deviations;
	}

	public function write_export_deviation($file, $departments, $withId = true)
	{
		$columns    = $withId ? ['Id', 'Phase', 'Steg'] : ['Phase', 'Steg'];
		$skipColumn = ['email', 'upload', 'eventanalysis'];
		$fields = $this->getDeviationFieldsByPage(array(1,2,3))['all'];
		$options = $this->getDropdown();

		foreach($fields as $column)
		{
			if(in_array($column->input, $skipColumn))
				continue;

			$columns[] = $column->title;
		}

		fputcsv($file, $columns);

		foreach($this->export($departments) as $a_id => $deviation)
		{
			$status = array_values($deviation)[0]->deviation_phase;
			if (array_values($deviation)[0]->reg_date_three == NULL )
				$status = 'incomplete';
			$step = $status == 'incomplete' ? ( 
				array_values($deviation)[0]->reg_date_two === null ? lang('stage1_reported') : lang('stage2_started')
			) : '';
			$status = $status == 'incomplete' ? lang('deviation_incomplete') : (
				$status == 'active' ? lang('deviation_active') : (
					$status == 'resolved' ? lang('deviation_resolved') : lang('deviation_archived')
				)
			);
			$row = $withId ? [$a_id, $status, $step] : [$status, $step];

			foreach($fields as $column)
			{
				if(in_array($column->input, $skipColumn))
					continue;

				if(isset($deviation[$column->df_id]))
				{
					$field = $deviation[$column->df_id];

					switch($field->input)
					{
						case 'input':
						case 'date':
						case 'text':
						case 'text_wysiwyg':
							$row[] = $field->answer;
							break;
						case 'department':
							$departments = [];

							if(is_array($field->answer))
							{
								foreach($field->answer as $department)
								{
									$answer = $options[$field->df_id][$department] ?? null;

									if($answer !== null)
									{
										$departments[] = $answer->name;
									}
								}
							}

							$row[] = implode(', ', $departments);
							break;
						case 'radio':
						case 'dropdown':
						case 'users':
							$answer = $options[$field->df_id][$field->answer] ?? null;
							$answer = $answer !== null ? $answer->name : '';

							$row[] = $answer;
							break;
						default:
							$row[] = '';
							break;
					}
				}
				else
				{
					$row[] = '';
				}
			}

			if(!empty($row))
			{
				fputcsv($file, $row);
			}
		}
	}

	public function deviation_stats() {
		$res = [];
		$sql = "SELECT count(*) as count FROM `deviation`
		where reg_date_one > '" . date("Y-m-d", strtotime("-12 month")) . "'";
		$res['deviations_12_months'] = $this->db->query($sql)->result()[0]->count;
		$sql = "SELECT count(*) as count FROM `deviation`
		where reg_date_one > '" . date("Y-m-d", strtotime("-10 day")) . "'";
		$res['deviations_10_days'] = $this->db->query($sql)->result()[0]->count;
		return $res;
	}

	public function report_data( $departments )
	{
		$data = $this->_getDeviationBySearchFilter($departments);

		$binding    = $data['binding']['current'];
		$deviations = [];

		$sql = $default_sql = "SELECT
				dfa.a_id,
				df.df_id,
				dn.reg_date_one,
				case when df.input = 'department' then 
					LOWER(CONCAT(
						LEFT(HEX(dd.de_id), 8), '-',
						MID(HEX(dd.de_id), 9, 4), '-',
						MID(HEX(dd.de_id), 13, 4), '-',
						MID(HEX(dd.de_id), 17, 4), '-',
						RIGHT(HEX(dd.de_id), 12)
					))
				else da.answer end as answer
			FROM
				deviation_fields_active AS dfa
			INNER JOIN
				deviation_fields AS df
					ON df.df_id=dfa.df_id AND ( df.input in ('dropdown', 'date', 'department', 'radio', 'users') ) AND df.page != 0
			LEFT JOIN
				deviation_answers AS da
					ON da.a_id=dfa.a_id AND da.df_id=dfa.df_id
			INNER JOIN
				deviation_department AS dd
					ON dd.a_id=dfa.a_id
						AND dd.de_id IN ?
			INNER JOIN
				deviation AS dn
					ON dn.a_id=dfa.a_id
						AND dn.status <> 'archived'
						-- AND regby_two IS NOT NULL AND regby_three IS NOT NULL
						AND dn.company_id = ?
			where df.input in ('dropdown', 'date', 'department', 'radio', 'users')
			GROUP BY
				dfa.a_id,
				df.df_id";

		$q = $this->db->query($sql, $binding);

		foreach($q->result() as $deviation)
		{
			$deviation->df_id = BIN_TO_UUID($deviation->df_id);
			$deviation->a_id  = BIN_TO_UUID($deviation->a_id);

			$deviations[$deviation->a_id][$deviation->df_id] = $deviation;
		}

		$sql = "SELECT
				status,
				reg_date_one,
				reg_date_two,
				reg_date_three,
				resolved_date
			FROM deviation 
			INNER JOIN
				deviation_department AS dd
					ON dd.a_id=deviation.a_id
						AND dd.de_id IN ?
			where company_id = ?
		";
		$q = $this->db->query($sql, $binding);

		return array(
			'overview'   => $q->result(),
			'deviations' => $deviations,
		);
	}

	public function search( $departments, $incomplete = false )
	{
		$data = $this->_getDeviationBySearchFilter($departments);

		$binding    = $data['binding']['current'];
		$search     = [];
		$list       = [];
		$post       = $data['post'];
		$deviations = [];
		$a_id       = $data['a_id'];
		$results    = $a_id !== null ? true : false;

		// Default SQL query
		$sql = $default_sql = "SELECT
					dfa.a_id,
					df.df_id,
					df.input,
					df.search,
					df.list,
					df.title,
					dn.status,
					dn.reg_date_one,
					dn.reg_date_two,
					dn.reg_date_three,
					(SELECT COUNT(*) AS the_count
					FROM deviation_attachment
					where deviation_attachment.a_id = dn.a_id
					) AS deviation_attachment_count,
					da.answer
				FROM
					deviation_fields_active AS dfa
				INNER JOIN
					deviation_fields AS df
						ON df.df_id=dfa.df_id AND (df.search =2 OR df.list=2 OR (df.input = 'input' and df.required_kvalprak = 1)) AND df.page != 0
				LEFT JOIN
					deviation_answers AS da
						ON da.a_id=dfa.a_id AND da.df_id=dfa.df_id
				INNER JOIN
					deviation_department AS dd
						ON dd.a_id=dfa.a_id
							AND dd.de_id IN ?
				INNER JOIN
					deviation AS dn
						ON dn.a_id=dfa.a_id
							AND company_id = ?
				" .
				($incomplete == true ? " where reg_date_three is null " : "")
				. "
				GROUP BY
					dfa.a_id,
					df.df_id
				ORDER BY
					dn.reg_date_three DESC,
					df.page ASC,
					df.order ASC,
					df.title ASC";

		// Look for specific deviations
		if(!empty($a_id))
		{
			$binding = array_merge($binding, [$a_id]);

			$sql = "SELECT
						dfa.a_id,
						df.df_id,
						df.input,
						df.search,
						df.list,
						df.title,
						dn.status,
						dn.reg_date_one,
						dn.reg_date_two,
						dn.reg_date_three,
						(SELECT COUNT(*) AS the_count
						FROM deviation_attachment
						where deviation_attachment.a_id = dn.a_id
						) AS deviation_attachment_count,
						da.answer
					FROM
						deviation_fields_active AS dfa
					INNER JOIN
						deviation_fields AS df
							ON df.df_id=dfa.df_id AND (df.search =2 OR df.list=2 OR (df.input = 'input' and df.required_kvalprak = 1)) AND df.page != 0
					LEFT JOIN
						deviation_answers AS da
							ON da.a_id=dfa.a_id AND da.df_id=dfa.df_id
					INNER JOIN
						deviation_department AS dd ON dd.a_id=dfa.a_id
						AND dd.de_id IN ?
					INNER JOIN
						deviation AS dn
							ON dn.a_id=dfa.a_id
								AND company_id = ?
					" .
					($incomplete == true ? " where reg_date_three is null " : "")
					. "
					WHERE
						dfa.a_id IN ?
					GROUP BY
						dfa.a_id,
						df.df_id
					ORDER BY
						dn.reg_date_three DESC,
						df.page ASC,
						df.order ASC,
						df.title ASC";
		}

		$q = $this->db->query($sql, $binding);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $deviation)
			{
				$deviation->df_id = BIN_TO_UUID($deviation->df_id);
				$deviation->a_id  = BIN_TO_UUID($deviation->a_id);
				if ($deviation->reg_date_three == NULL )
						$deviation->status = 'incomplete';

				if( $deviation->search == 2 && ! isset($search[$deviation->df_id]) )
					$search[$deviation->df_id] = $deviation;

				if( $deviation->list == 2 && ! in_array($deviation->df_id,$list) )
					$list[$deviation->df_id] = $deviation->title;

				$deviations[$deviation->a_id][$deviation->df_id] = $deviation;
			}
		}
		else if($data['search']['department']['empty'])
		{
			$q = $this->db->query($default_sql, $data['binding']['default']);

			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $deviation)
				{
					$deviation->df_id = BIN_TO_UUID($deviation->df_id);
					$deviation->a_id  = BIN_TO_UUID($deviation->a_id);

					if ($deviation->reg_date_three == NULL )
						$deviation->status = 'incomplete';

					if( $deviation->search == 2 && ! isset($search[$deviation->df_id]) )
						$search[$deviation->df_id] = $deviation;

					if( $deviation->list == 2 && ! in_array($deviation->df_id,$list) )
						$list[$deviation->df_id] = $deviation->title;
				}

				$results = false;
			}
		}

		return array(
			'search'     => $search,
			'list'       => $list,
			'post'       => $post,
			'deviations' => $deviations,
			'results'    => $results,
		);
	}

	public function save_attachments( $attachments )
	{
		return $this->db->insert($this->db_table('deviation_attachment'),$attachments);
	}

	public function delete_attachments( $attachment_id )
	{
		return $this->db->delete($this->db_table('deviation_attachment'),array('attachment_id' => UUID_TO_BIN($attachment_id)));
	}

	public function get_field($id)
	{
		$field = NULL;

		$q = $this->db
				->where('df_id', UUID_TO_BIN($id))
				->limit(1)
				->get($this->db_table('deviation_fields'));
		if( $q->num_rows() === 1)
		{
			$field = $q->row();
			$field->df_id = BIN_TO_UUID($field->df_id);
		}
		return $field;
	}

	public function get_fields()
	{
		$sql = "SELECT
					`df_id`,
					`input`,
					`required`,
					`search`,
					`list`,
					`title`
				FROM
					deviation_fields
				ORDER BY
					`title` ASC,
					`description` ASC";
		$q = $this->db->query($sql);
		$fields = array();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$row->df_id = BIN_TO_UUID($row->df_id);
				$fields[$row->df_id] = $row;
			}
		}

		return $fields;
	}

	public function get_field_answer($id)
	{
		$q = $this->db
				->where('df_id', UUID_TO_BIN($id))
				->limit(1)
				->get($this->db_table('deviation_answers'));
		if( $q->num_rows() === 1)
		{
			return TRUE;
		}
		return FALSE;
	}

	public function get_field_answers($id)
	{
		$q = $this->db
				->select('answer')
				->where('df_id', UUID_TO_BIN($id))
				->group_by('answer')
				->get($this->db_table('deviation_answers'));
		$answers = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$answers[] = $row->answer;
			}
		}
		return $answers;
	}

	public function get_email($a_id)
	{
		$q = $this->db
				->select('df_id,user_id')
				->where('a_id', UUID_TO_BIN($a_id))
				->get($this->db_table('deviation_email'));
		$users = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$users[BIN_TO_UUID($row->df_id)][] = BIN_TO_UUID($row->user_id);
			}
		}
		return $users;
	}

	public function get_email_default($field, $department)
	{
		if( is_array($department) )
		{
			$bin = [];
			foreach($department as $d)
			{
				$bin[] = UUID_TO_BIN($d);
			}
			$this->db->where_in('group_id', $bin);
		}
		else
		{
			$this->db->where('group_id', UUID_TO_BIN($department));
		}

		$q = $this->db
				->select('user_id')
				->where('df_id', UUID_TO_BIN($field))
				->get($this->db_table('deviation_email_default'));
		$users = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$users[] = BIN_TO_UUID($row->user_id);
			}
		}
		return $users;
	}

	public function get_dropdown($id)
	{
		$dropdown = NULL;

		$q = $this->db
				->where('dd_id', UUID_TO_BIN($id))
				->limit(1)
				->get($this->db_table('deviation_dropdown'));
		if( $q->num_rows() === 1)
		{
			$dropdown = $q->row();
			$dropdown->dd_id = BIN_TO_UUID($dropdown->dd_id);
			$dropdown->df_id = BIN_TO_UUID($dropdown->df_id);
		}
		return $dropdown;
	}

	public function add_field()
	{
		$field_id = UUIDv4();
		$data = [
			'df_id'       => UUID_TO_BIN($field_id),
			'input'       => $this->input->post('deviation_field_input'),
			'required'    => $this->input->post('deviation_field_required'),
			'search'      => $this->input->post('deviation_field_search'),
			'list'        => $this->input->post('deviation_field_list'),
			'title'       => $this->input->post('deviation_field_title'),
			'description' => $this->input->post('deviation_field_description'),
		];
		if( $this->db->insert($this->db_table('deviation_fields'),$data) )
		{
			return $field_id;
		}
		else
		{
			FALSE;
		}
	}

	public function add_dropdown($df_id, $title = NULL, $default = 0)
	{
		$data = [
			'dd_id'   => UUID_TO_BIN(UUIDv4()),
			'df_id'   => UUID_TO_BIN($df_id),
			'title'   => ! empty($title) ? $title : $this->input->post('deviation_field_title'),
			'default' => $default
		];
		return $this->db->insert($this->db_table('deviation_dropdown'),$data);
	}

	public function edit_field($field)
	{
		$field->df_id       = UUID_TO_BIN($field->df_id);
		$field->input       = $this->input->post('deviation_field_input')       ? $this->input->post('deviation_field_input')       : $field->input;
		$field->required    = $this->input->post('deviation_field_required')    ? $this->input->post('deviation_field_required')    : $field->required;
		$field->search      = $this->input->post('deviation_field_search')      ? $this->input->post('deviation_field_search')      : $field->search;
		$field->list        = $this->input->post('deviation_field_list')        ? $this->input->post('deviation_field_list')        : $field->list;
		$field->title       = $this->input->post('deviation_field_title')       ? $this->input->post('deviation_field_title')       : $field->title;
		$field->description = $this->input->post('deviation_field_description') ? $this->input->post('deviation_field_description') : $field->description;

		$q = $this->db
				->where('df_id', $field->df_id)
				->update($this->db_table('deviation_fields'),$field);
		return $q;
	}

	public function edit_dropdown($dropdown)
	{
		$dropdown->dd_id = UUID_TO_BIN($dropdown->dd_id);
		$dropdown->df_id = UUID_TO_BIN($dropdown->df_id);
		$dropdown->title = $this->input->post('deviation_field_title') ? $this->input->post('deviation_field_title') : $dropdown->title;

		$q = $this->db
				->where('dd_id', $dropdown->dd_id)
				->update($this->db_table('deviation_dropdown'),$dropdown);
		return $q;
	}

	public function delete_field($id)
	{
		$this->db->delete($this->db_table('deviation_fields'), [
			'df_id' => UUID_TO_BIN($id)
		]);

		$this->db->delete($this->db_table('deviation_dropdown'), [
			'df_id' => UUID_TO_BIN($id)
		]);
	}

	public function delete_dropdown($id)
	{
		$this->db->delete($this->db_table('deviation_dropdown'), [
			'dd_id' => UUID_TO_BIN($id)
		]);
	}

	public function change_email($field, $department)
	{
		$field = UUID_TO_BIN($field);
		$department = UUID_TO_BIN($department);
		if( $this->change_email_remove($field, $department) === FALSE) { return FALSE; }

		$users = $this->_get_contacts();
		if( empty($users) ) { return TRUE; }

		$data = array();
		foreach($users as $user_id)
		{
			$data[] = array(
				'df_id'    => $field,
				'user_id'  => UUID_TO_BIN($user_id),
				'group_id' => $department
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('deviation_email_default'),$data);

		return FALSE;
	}

	private function change_email_remove($field, $department)
	{
		return $this->db->delete($this->db_table('deviation_email_default'),[
			'df_id' => $field,
			'group_id' => $department,
		]);
	}

	public function add_empty_dropdown($df_id, $dd_id)
	{
		$data = [
			'df_id' => UUID_TO_BIN($df_id),
			'dd_id' => UUID_TO_BIN($dd_id),
			'order' => 0,
		];
		return $this->db->insert($this->db_table('deviation_dropdown'),$data);
	}

	public function sort_dropdown($sort_order)
	{
		$data = [];
		foreach($sort_order as $order => $dd_id)
		{
			$data[] = [
				'order' => $order,
				'dd_id' => UUID_TO_BIN($dd_id)
			];
		}
		return $this->db->update_batch($this->db_table('deviation_dropdown'), $data, 'dd_id');
	}

	public function sort_fields($sort, $fields)
	{
		if( empty(array_filter($sort)) OR empty($fields) ) { return NULL; }

		$data = [];
		$available = [];
		$sortOrder = 0;

		foreach($sort as $page => $sort_order)
		{
			$selected = [
				'upload' => [],
				'email'  => []
			];

			foreach($sort_order as $order => $df_id)
			{
				if( $page != 0 )
				{
					if
					(
						( $fields[$df_id]->input == 'upload' && empty($selected['upload']) ) OR
						( $fields[$df_id]->input == 'email' && empty($selected['email']) )
					)
					{
						$selected[$fields[$df_id]->input][] = $df_id;
						$data[] = [
							'order' => $order,
							'page'  => $page,
							'df_id' => UUID_TO_BIN($df_id)
						];
					}
					elseif
					(
						( in_array($fields[$df_id]->input, ['upload','email']) ) OR
						( $fields[$df_id]->input == 'department' && $page != 1)
					)
					{
						$available[] = $df_id;
					}
					else
					{
						$data[] = [
							'order' => $order,
							'page'  => $page,
							'df_id' => UUID_TO_BIN($df_id)
						];
					}
				}
				else
				{
					$data[] = [
						'order' => $order,
						'page'  => $page,
						'df_id' => UUID_TO_BIN($df_id)
					];
					++$sortOrder;
				}
			}
		}

		if( ! empty($available) )
		{
			foreach($available as $df_id)
			{
				$data[] = [
					'order' => $sortOrder,
					'page'  => 0,
					'df_id' => UUID_TO_BIN($df_id)
				];
				++$sortOrder;
			}
		}

		if( ! empty($data) )
		{
			return $this->db->update_batch($this->db_table('deviation_fields'), $data, 'df_id');
		}

		return NULL;
	}

}