<?php $this->load->view('template/header'); ?>

<style>
/* تحسينات شاملة للصفحة والجدول */

/* إصلاح مشكلة الـ header الثابت */
.content-header {
	position: sticky !important;
	top: 0 !important;
	z-index: 1000 !important;
	background: #fff !important;
	border-bottom: 1px solid #ddd !important;
	margin-bottom: 20px !important;
}

/* إصلاح مشكلة الـ sidebar المغلق */
@media (min-width: 768px) {
	.sidebar-collapse .content-wrapper,
	.sidebar-collapse .right-side,
	.sidebar-collapse .main-footer {
		margin-left: 0 !important;
	}
	
	.content {
		padding: 15px;
	}
}

body.sidebar-collapse .content-wrapper {
	margin-left: 0;
}

body.sidebar-mini.sidebar-collapse .content-wrapper {
	margin-left: 50px;
}

/* تحسين الإحصائيات للشاشات المختلفة - أكثر طولاً */
.info-box {
	border-radius: 6px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	margin-bottom: 15px;
	min-height: 80px;
}

.info-box-content {
	padding: 12px 6px;
	overflow: hidden;
}

.info-box-text {
	font-size: 11px;
	font-weight: 600;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	line-height: 1.2;
}

.info-box-number {
	font-size: 20px;
	font-weight: bold;
	line-height: 1.1;
}

.info-box-icon {
	width: 60px !important;
	line-height: 80px !important;
	font-size: 22px !important;
}

/* Large screens: All 5 in a row - زيادة العرض وطول الإحصائيات */
@media (min-width: 1200px) {
	.col-lg-2 { 
		width: 20%; 
		float: left;
	}
	
	.info-box {
		min-height: 90px;
	}
	
	.info-box-text {
		font-size: 12px;
	}
	
	.info-box-number {
		font-size: 22px;
	}
	
	.info-box-icon {
		width: 70px !important;
		line-height: 90px !important;
		font-size: 24px !important;
	}
	
	.info-box-content {
		margin-left: 70px !important;
		padding: 15px 8px !important;
	}
}

/* Medium screens: 3 on top, 2 below */
@media (max-width: 1199px) and (min-width: 992px) {
	.col-lg-2 {
		width: 33.333333%;
		float: left;
	}
	
	.info-box {
		min-height: 85px;
	}
	
	.info-box-text {
		font-size: 11px;
	}
	
	.info-box-number {
		font-size: 20px;
	}
	
	.info-box-icon {
		width: 65px !important;
		line-height: 85px !important;
		font-size: 22px !important;
	}
	
	.info-box-content {
		margin-left: 65px !important;
		padding: 12px 6px !important;
	}
}

/* Tablet screens: 2 per row */
@media (min-width: 768px) and (max-width: 991px) {
	.col-md-4 {
		width: 50%;
		float: left;
	}
	
	.info-box {
		min-height: 80px;
	}
	
	.info-box-text {
		font-size: 11px;
	}
	
	.info-box-number {
		font-size: 18px;
	}
	
	.info-box-icon {
		width: 60px !important;
		line-height: 80px !important;
		font-size: 20px !important;
	}
	
	.info-box-content {
		margin-left: 60px !important;
		padding: 12px 6px !important;
	}
}

/* Small screens: 2 per row */
@media (max-width: 767px) {
	.col-sm-6, .col-xs-12 {
		width: 50%;
		float: left;
		margin-bottom: 10px;
	}
	
	.info-box {
		min-height: 75px;
	}
	
	.info-box-icon {
		width: 55px !important;
		line-height: 75px !important;
		font-size: 18px !important;
	}
	
	.info-box-content {
		margin-left: 55px !important;
		padding: 10px 5px !important;
	}
	
	.info-box-text {
		font-size: 10px;
	}
	
	.info-box-number {
		font-size: 16px;
	}
}

.row {
	margin-left: -15px;
	margin-right: -15px;
}

.row:after {
	content: "";
	display: table;
	clear: both;
}

.row > [class*="col-"] {
	padding-left: 15px;
	padding-right: 15px;
}

.table-compact {
	font-size: 14px;
	table-layout: fixed;
}

.table-compact th {
	font-weight: 600;
	background-color: #f8f9fa !important;
	border-bottom: 2px solid #dee2e6;
	position: sticky;
	top: 0;
	z-index: 99;
}

.table-compact td {
	vertical-align: middle;
	padding: 8px 6px;
	word-wrap: break-word;
	word-break: break-word;
}

.btn-sm {
	padding: 3px 6px;
	font-size: 11px;
	line-height: 1.3;
	border-radius: 3px;
	min-width: 30px;
}

.action-buttons {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 2px;
}

.table-responsive {
	border: none;
	overflow-x: auto;
	width: 100%;
}

/* إصلاح عام للجدول */
.table-compact {
	width: 100%;
	margin-bottom: 0;
}

.table-compact thead th {
	position: sticky;
	top: 0;
	background-color: #f8f9fa !important;
	z-index: 10;
	border-bottom: 2px solid #dee2e6;
}

/* ضمان التناسق في جميع الحالات */
.content-wrapper {
	transition: margin-left 0.3s ease-in-out;
}

body.sidebar-mini.sidebar-collapse .content-wrapper {
	margin-left: 50px !important;
}

body.sidebar-collapse .content-wrapper {
	margin-left: 0 !important;
}

/* إصلاح التوافق مع الـ sidebar المغلق */
@media (min-width: 768px) {
	body.sidebar-collapse .table-compact th,
	body.sidebar-collapse .table-compact td {
		padding: 8px 6px;
	}

	body.sidebar-collapse .col-md-10 {
		width: 90% !important;
		margin-left: 5% !important;
	}

	/* إصلاح تناسق رأس الجدول مع الجسم عند إغلاق الـ sidebar */
	body.sidebar-collapse .table-responsive {
		width: 100%;
		overflow-x: auto;
	}

	body.sidebar-collapse .table-compact {
		width: 100%;
		table-layout: auto;
	}

	body.sidebar-collapse .table-compact thead th {
		white-space: nowrap;
		text-align: center;
		vertical-align: middle;
	}

	body.sidebar-collapse .table-compact tbody td {
		text-align: center;
		vertical-align: middle;
	}

	/* ضمان محاذاة الأعمدة بشكل صحيح */
	body.sidebar-collapse .table-compact th:nth-child(1),
	body.sidebar-collapse .table-compact td:nth-child(1) {
		width: 25%;
		text-align: left;
	}

	body.sidebar-collapse .table-compact th:nth-child(2),
	body.sidebar-collapse .table-compact td:nth-child(2) {
		width: 15%;
	}

	body.sidebar-collapse .table-compact th:nth-child(3),
	body.sidebar-collapse .table-compact td:nth-child(3) {
		width: 20%;
	}

	body.sidebar-collapse .table-compact th:nth-child(4),
	body.sidebar-collapse .table-compact td:nth-child(4) {
		width: 20%;
	}

	body.sidebar-collapse .table-compact th:nth-child(5),
	body.sidebar-collapse .table-compact td:nth-child(5) {
		width: 20%;
	}
}

@media (max-width: 767px) {
	.table-compact {
		font-size: 12px;
	}
	
	.table-compact th {
		font-size: 13px;
		padding: 6px 4px;
	}
	
	.table-compact td {
		padding: 6px 4px;
	}
	
	/* إخفاء عمود التاريخ في الشاشات الصغيرة */
	.table th:nth-child(4), 
	.table td:nth-child(4) { 
		display: none; 
	}
	
	.col-md-10 {
		width: 100% !important;
		margin-left: 0 !important;
	}
}

/* تحسين عام للأيقونات والعناصر */
.entity-icon {
	font-size: 18px;
	width: 20px;
	text-align: center;
}

.info-box-icon {
	text-align: center;
	font-weight: 300;
	color: rgba(255,255,255,0.8);
	overflow: hidden;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}

/* تحسين العنوان الثابت */
.box-header {
	background: #fff !important;
	border-bottom: 1px solid #ddd !important;
}

/* ضمان عدم تداخل الـ sticky headers */
.content-header {
	z-index: 1001 !important;
}

.box-header {
	z-index: 1000 !important;
}

.table-compact thead th {
	z-index: 999 !important;
}
</style>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header">
			<h1>
				<?php echo lang('shareable_title'); ?>
				<small><?php echo lang('shareable_subtitle'); ?></small>
			</h1>
			<ol class="breadcrumb">
				<li><a href="<?php echo site_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> <?php echo lang('dashboard'); ?></a></li>
				<li><a href="<?php echo site_url('admin/documents'); ?>"><?php echo lang('documents'); ?></a></li>
				<li class="active"><?php echo lang('shareable_title'); ?></li>
			</ol>
		</section>

	<!-- Main content -->
	<section class="content">
		<!-- Info boxes -->
		<div class="row">
			<div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
				<div class="info-box" style="min-height: 90px;">
					<span class="info-box-icon bg-aqua" style="line-height: 90px; font-size: 24px; width: 70px;"><i class="fa fa-share-alt"></i></span>
					<div class="info-box-content" style="padding: 15px 8px; margin-left: 70px;">
						<span class="info-box-text" style="font-size: 13px; font-weight: 600; white-space: nowrap;">Totalt delade</span>
						<span class="info-box-number" style="font-size: 22px; font-weight: bold;"><?php echo $stats['total_shared']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
				<div class="info-box" style="min-height: 90px;">
					<span class="info-box-icon bg-blue" style="line-height: 90px; font-size: 24px; width: 70px;"><i class="fa fa-file-o"></i></span>
					<div class="info-box-content" style="padding: 15px 8px; margin-left: 70px;">
						<span class="info-box-text" style="font-size: 13px; font-weight: 600; white-space: nowrap;">Dokument</span>
						<span class="info-box-number" style="font-size: 22px; font-weight: bold;"><?php echo isset($stats['by_type']['document']) ? $stats['by_type']['document'] : 0; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
				<div class="info-box" style="min-height: 90px;">
					<span class="info-box-icon bg-orange" style="line-height: 90px; font-size: 24px; width: 70px;"><i class="fa fa-exclamation-triangle"></i></span>
					<div class="info-box-content" style="padding: 15px 8px; margin-left: 70px;">
						<span class="info-box-text" style="font-size: 13px; font-weight: 600; white-space: nowrap;">Avvikelser</span>
						<span class="info-box-number" style="font-size: 22px; font-weight: bold;"><?php echo isset($stats['by_type']['deviation']) ? $stats['by_type']['deviation'] : 0; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
				<div class="info-box" style="min-height: 90px;">
					<span class="info-box-icon bg-green" style="line-height: 90px; font-size: 24px; width: 70px;"><i class="fa fa-search-plus"></i></span>
					<div class="info-box-content" style="padding: 15px 8px; margin-left: 70px;">
						<span class="info-box-text" style="font-size: 13px; font-weight: 600; white-space: nowrap;">Händelseanalyser</span>
						<span class="info-box-number" style="font-size: 22px; font-weight: bold;"><?php echo isset($stats['by_type']['eventanalysis']) ? $stats['by_type']['eventanalysis'] : 0; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
				<div class="info-box" style="min-height: 90px;">
					<span class="info-box-icon bg-red" style="line-height: 90px; font-size: 24px; width: 70px;"><i class="fa fa-flash"></i></span>
					<div class="info-box-content" style="padding: 15px 8px; margin-left: 70px;">
						<span class="info-box-text" style="font-size: 13px; font-weight: 600; white-space: nowrap;">Riskbedömningar</span>
						<span class="info-box-number" style="font-size: 22px; font-weight: bold;"><?php echo isset($stats['by_type']['riskassessment']) ? $stats['by_type']['riskassessment'] : 0; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-4 col-sm-6 col-xs-12">
			</div>
		</div>

		<!-- Documents list -->
		<div class="row">
			<div class="col-md-10 col-md-offset-1 col-sm-12">
				<div class="box">
					<div class="box-header with-border" style="position: sticky; top: 0; z-index: 100; background: white;">
						<?php
						// Ensure uniqueness by share_id to prevent any display duplicates
						$unique_docs = array();
						$seen_share_ids = array();
						if (!empty($shared_documents)) {
							foreach ($shared_documents as $doc) {
								if (!in_array($doc->share_id, $seen_share_ids)) {
									$unique_docs[] = $doc;
									$seen_share_ids[] = $doc->share_id;
								}
							}
						}
						?>
						<h3 class="box-title" style="font-size: 18px;">
							Lista över delade objekt
							<small style="font-size: 14px;">(<?php echo count($unique_docs); ?> delningar)</small>
						</h3>
					</div>
					<div class="box-body">
						<?php if (!empty($unique_docs)): ?>
							<div class="table-responsive">
								<table class="table table-bordered table-striped table-compact" style="margin-bottom: 0; table-layout: fixed; font-size: 13px; width: 90%; margin: 0 auto;">
									<thead style="background-color: #f5f5f5; position: sticky; top: 0; z-index: 99;">
										<tr>
											<th style="width: 40%; padding: 10px 8px; font-weight: 600; vertical-align: middle; font-size: 14px;">Namn</th>
											<th style="width: 18%; padding: 10px 8px; font-weight: 600; vertical-align: middle; font-size: 14px;">Typ</th>
											<th style="width: 18%; padding: 10px 8px; font-weight: 600; vertical-align: middle; font-size: 14px;">Delat av</th>
											<th style="width: 14%; padding: 10px 8px; font-weight: 600; vertical-align: middle; font-size: 14px;">Delningsdatum</th>
											<th style="width: 10%; padding: 10px 8px; font-weight: 600; text-align: center; vertical-align: middle; font-size: 14px;">Åtgärder</th>
										</tr>
									</thead>
									<tbody>
										<?php foreach ($unique_docs as $doc): ?>
											<tr style="border-bottom: 1px solid #ddd;" data-entity-type="<?php echo htmlspecialchars($doc->entity_type); ?>" data-share-id="<?php echo htmlspecialchars($doc->share_id); ?>">
												<td style="padding: 12px 10px; vertical-align: middle;">
													<div class="document-info" style="display: flex; align-items: center; gap: 12px;">
														<?php 
														// Set icon based on entity type
														$entity_icon = '';
														$entity_color = '#666';
														switch($doc->entity_type) {
															case 'document':
																if (!empty($doc->file_ext)) {
																	$file_ext = strtolower(ltrim($doc->file_ext, '.'));
																	switch($file_ext) {
																		case 'docx':
																		case 'doc':
																			$entity_icon = '<img src="/resources/css/images/file_docx.svg" class="entity-icon" style="width: 24px; height: 24px;" alt="DOCX"/>';
																			break;
																		case 'xlsx':
																		case 'xls':
																			$entity_icon = '<img src="/resources/css/images/file_xlsx.svg" class="entity-icon" style="width: 24px; height: 24px;" alt="XLSX"/>';
																			break;
																		case 'pptx':
																		case 'ppt':
																			$entity_icon = '<img src="/resources/css/images/file_pptx.svg" class="entity-icon" style="width: 24px; height: 24px;" alt="PPTX"/>';
																			break;
																		default:
																			$entity_icon = '<i class="fa fa-file-o entity-icon" style="color: #666; font-size: 24px;"></i>';
																			break;
																	}
																} else {
																	$entity_icon = '<i class="fa fa-file-o entity-icon" style="color: #666; font-size: 24px;"></i>';
																}
																break;
															case 'deviation':
																$entity_icon = '<i class="fa fa-exclamation-triangle entity-icon" style="color: #f39c12; font-size: 24px;"></i>';
																break;
															case 'eventanalysis':
																$entity_icon = '<i class="fa fa-search-plus entity-icon" style="color: #00a65a; font-size: 24px;"></i>';
																break;
															case 'riskassessment':
																$entity_icon = '<i class="fa fa-flash entity-icon" style="color: #dd4b39; font-size: 24px;"></i>';
																break;
															default:
																$entity_icon = '<i class="fa fa-file-o entity-icon" style="color: #666; font-size: 24px;"></i>';
																break;
														}
														echo $entity_icon;
														?>
														<div>
															<strong style="font-size: 15px;">
																<?php 
																// Ensure proper base URL with HTTP protocol
																$base_url = rtrim(config_item('base_url'), '/');
																if (empty($base_url) || !parse_url($base_url, PHP_URL_SCHEME)) {
																	$base_url = 'http' . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 's' : '') . '://' . $_SERVER['HTTP_HOST'];
																}
																
																$view_url = '';
																switch($doc->entity_type) {
																	case 'document':
																		$view_url = $base_url . '/documentview/view/' . $doc->share_id;
																		break;
																	case 'deviation':
																		$view_url = $base_url . '/deviationview/view/' . $doc->share_id;
																		break;
																	case 'eventanalysis':
																		$view_url = $base_url . '/eventanalysisview/view/' . $doc->share_id;
																		break;
																	case 'riskassessment':
																		$view_url = $base_url . '/riskassessmentsview/view/' . $doc->share_id;
																		break;
																}
																?>
																<a href="<?php echo $view_url; ?>" 
																   target="_blank" 
																   style="text-decoration: none; color: #337ab7; font-weight: 600;">
																	<?php echo htmlspecialchars($doc->entity_name ?? ''); ?>
																</a>
															</strong>
														</div>
													</div>
												</td>
												<td style="padding: 12px 10px; vertical-align: middle; font-size: 14px;">
													<?php 
													$type_labels = array(
														'document' => 'Dokument',
														'deviation' => 'Avvikelse',
														'eventanalysis' => 'Händelseanalys',
														'riskassessment' => 'Riskbedömning'
													);
													echo isset($type_labels[$doc->entity_type]) ? $type_labels[$doc->entity_type] : ucfirst($doc->entity_type);
													?>
												</td>
												<td style="padding: 12px 10px; vertical-align: middle; font-size: 14px;"><?php echo htmlspecialchars($doc->shared_by_name ?? ''); ?></td>
												<td style="padding: 12px 10px; vertical-align: middle; font-size: 14px;">
													<span title="<?php echo sprintf(lang('shareable_first_shared'), date('Y-m-d H:i:s', strtotime($doc->shared_date))); ?>">
														<?php echo date('Y-m-d H:i', strtotime($doc->shared_date)); ?>
													</span>
												</td>
												<td style="text-align: center; padding: 8px 4px; vertical-align: middle;">
													<div style="display: flex; gap: 2px; justify-content: center; align-items: center;">
														<button type="button" class="btn btn-info btn-sm" 
																onclick="copyShareLink('<?php echo $view_url; ?>')"
																title="<?php echo lang('shareable_copy_link'); ?>"
																style="padding: 3px 6px; font-size: 11px; margin: 0; min-width: 30px;">
															<i class="fa fa-copy"></i>
														</button>
														<button type="button" class="btn btn-danger btn-sm" 
																onclick="removeShare('<?php echo htmlspecialchars($doc->share_id ?? ''); ?>')"
																title="<?php echo lang('shareable_remove_share'); ?>"
																style="padding: 3px 6px; font-size: 11px; margin: 0; min-width: 30px;">
															<i class="fa fa-times"></i>
														</button>
													</div>
												</td>
											</tr>
										<?php endforeach; ?>
									</tbody>
								</table>
							</div>

						<?php else: ?>
							<div class="alert alert-info text-center">
								<i class="fa fa-info-circle"></i>
								<?php echo lang('shareable_no_documents'); ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->

<script>
function copyShareLink(url) {
	if (navigator.clipboard) {
		navigator.clipboard.writeText(url).then(function() {
			showNotification('<?php echo lang('shareable_link_copied'); ?>', 'success');
		}).catch(function() {
			fallbackCopyTextToClipboard(url);
		});
	} else {
		fallbackCopyTextToClipboard(url);
	}
}

function fallbackCopyTextToClipboard(text) {
	var textArea = document.createElement("textarea");
	textArea.value = text;
	document.body.appendChild(textArea);
	textArea.focus();
	textArea.select();
	try {
		var successful = document.execCommand('copy');
		if (successful) {
			showNotification('<?php echo lang('shareable_link_copied'); ?>', 'success');
		} else {
			showNotification('<?php echo lang('shareable_copy_failed'); ?>', 'error');
		}
	} catch (err) {
		showNotification('<?php echo lang('shareable_copy_failed'); ?>', 'error');
	}
	document.body.removeChild(textArea);
}

function removeShare(shareId) {
	if (!confirm('<?php echo lang('shareable_confirm_remove'); ?>')) {
		return;
	}

	var button = $('button[onclick="removeShare(\'' + shareId + '\')"]');
	var row = button.closest('tr');
	
	// Get the entity type from data attribute
	var entityType = row.data('entity-type');

	$.ajax({
		url: '<?php echo site_url('admin/documents/remove_share'); ?>',
		type: 'POST',
		data: {
			share_id: shareId,
			'<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
		},
		dataType: 'json',
		beforeSend: function() {
			// Disable the button and show loading state
			button.prop('disabled', true);
			button.html('<i class="fa fa-spinner fa-spin"></i>');
		},
		success: function(response) {
			if (response.success) {
				showNotification(response.message, 'success');
				// Remove the row from the table with animation
				row.fadeOut(500, function() {
					$(this).remove();
					// Update the document count and statistics
					updateDocumentCount(entityType);
				});
			} else {
				showNotification(response.message || '<?php echo lang('shareable_remove_failed'); ?>', 'error');
				// Reset button state
				button.prop('disabled', false);
				button.html('<i class="fa fa-times"></i>');
			}
		},
		error: function(xhr, status, error) {
			console.log('Failed to remove share:', error);
			console.log('Response:', xhr.responseText);
			showNotification('<?php echo lang('shareable_remove_failed'); ?>', 'error');
			// Reset button state
			button.prop('disabled', false);
			button.html('<i class="fa fa-times"></i>');
		}
	});
}

function updateDocumentCount(entityType) {
	console.log('Updating counts for entity type:', entityType);

	// Wait a moment for the row to be removed, then count remaining rows
	setTimeout(function() {
		var remainingRows = $('tbody tr:visible').length;
		console.log('Remaining visible rows:', remainingRows);

		// Update total count
		$('.box-title small').text('(' + remainingRows + ' delningar)');

		// Update total shared count (first info box)
		var totalBox = $('.info-box .bg-aqua + .info-box-content .info-box-number');
		var currentTotal = parseInt(totalBox.text()) || 0;
		var newTotal = Math.max(0, currentTotal - 1);
		totalBox.text(newTotal);
		console.log('Updated total count from', currentTotal, 'to', newTotal);

		// If no documents left, show the no documents message
		if (remainingRows === 0) {
			$('.table-responsive').html('<div class="alert alert-info text-center"><i class="fa fa-info-circle"></i> <?php echo lang('shareable_no_documents'); ?></div>');
		}

		// Update specific type count based on entity type
		if (entityType) {
			var targetSelector = '';
			switch(entityType) {
				case 'document':
					targetSelector = '.info-box .bg-blue + .info-box-content .info-box-number';
					break;
				case 'deviation':
					targetSelector = '.info-box .bg-orange + .info-box-content .info-box-number';
					break;
				case 'eventanalysis':
					targetSelector = '.info-box .bg-green + .info-box-content .info-box-number';
					break;
				case 'riskassessment':
					targetSelector = '.info-box .bg-red + .info-box-content .info-box-number';
					break;
			}

			if (targetSelector) {
				var typeBox = $(targetSelector);
				var currentTypeCount = parseInt(typeBox.text()) || 0;
				var newTypeCount = Math.max(0, currentTypeCount - 1);
				typeBox.text(newTypeCount);
				console.log('Updated', entityType, 'count from', currentTypeCount, 'to', newTypeCount);
			}
		}

	}, 100); // Small delay to ensure DOM is updated
}

function showNotification(message, type) {
	var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
	var notification = '<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
		'<button type="button" class="close" data-dismiss="alert">&times;</button>' +
		message +
		'</div>';
	$('body').append(notification);
	setTimeout(function() {
		$('.alert').fadeOut();
	}, 3000);
}

// إصلاح تناسق الجدول عند تغيير حالة الـ sidebar
function adjustTableLayout() {
	setTimeout(function() {
		// إعادة حساب عرض الجدول
		$('.table-responsive').css('width', '100%');
		$('.table-compact').css('width', '100%');

		// إعادة تحديد موضع رأس الجدول
		$('.table-compact thead th').each(function(index) {
			var $th = $(this);
			var $correspondingTd = $('.table-compact tbody tr:first td').eq(index);
			if ($correspondingTd.length) {
				$th.css('width', $correspondingTd.outerWidth() + 'px');
			}
		});

		console.log('Table layout adjusted for sidebar state');
	}, 350); // انتظار انتهاء انيميشن الـ sidebar
}

// مراقبة تغيير حالة الـ sidebar
$(document).ready(function() {
	// مراقبة النقر على زر الـ sidebar toggle
	$(document).on('click', '[data-toggle="push-menu"]', function() {
		adjustTableLayout();
	});

	// مراقبة تغيير حجم النافذة
	$(window).on('resize', function() {
		adjustTableLayout();
	});

	// تشغيل التعديل عند تحميل الصفحة
	adjustTableLayout();
});
</script>

<?php $this->load->view('template/footer'); ?>
