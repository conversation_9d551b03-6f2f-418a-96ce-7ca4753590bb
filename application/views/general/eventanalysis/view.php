<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="container">
			<!-- Content Header (Page header) -->
			<section class="content-header margin-bottom">
				<div class="float-right btn-group">
					<?php
					if( ! empty($rights['update']) OR $getEventAnalysis->us_id === $this->auth_user_id )
					{
						echo icon_anchor('eventanalysis/edit', $getEventAnalysis->id, '<i class="fa fa-pencil" aria-hidden="true"></i>',
							array(
							'title' => 'Redigera händelseanalys',
							'class' => 'btn btn-primary',
							)
						);
					}
					?>
					<a class="btn btn-default" title="<?php echo lang('deviation_share') ?>" data-toggle="modal" data-target="#myModal">
						<i class="fa fa-share-alt" aria-hidden="true" style="margin-right: 5px"></i>
					</a>
					<a href="javascript:goBack('/eventanalysis');" title="<?php echo lang('back') ?>" class="btn btn-default"><i class="fa fa-reply" aria-hidden="true"></i></a>
				</div>
				<h1>Händelseanalys
					<small>Visa</small>
				</h1>
			</section>
			<?php // @STEP2: html_escape ?>
			<!-- Main content -->
			<section class="content">
				<div class="no-print">
					<div class="callout callout-info" style="color: #172B4D !important;">
						När en avvikelse har inträffat är den ibland så allvarligt att lagen föreskriver att man skall göra en djupare händelseanalys än den som man gör i den vanliga avvikelsehanteringen.
					</div>
				</div>
				<div class="row">
					<div class="col-md-4">
						<div class="box box-solid">
							<div class="box-body">
								<dl class="description-list">
									<?php
									foreach($fields as $key => $val):
										viewForm($val['id'],$val['input'],$val['required'],$val['title'],$val['description'],$val['value']);
									endforeach;
									foreach($fieldsDone as $key => $val):
										if(!empty($val['value']))
										viewForm($val['id'],$val['input'],$val['required'],$val['title'],$val['description'],$val['value']);
									endforeach;
									?>
								</dl>
							</div>
						</div>
						<?php if( ! empty($getEventAnalysisDeviationMap) ): ?>
						<div class="box box-solid">
							<div class="box-header with-border">
								<h3 class="box-title"><?php echo count($getEventAnalysisDeviationMap) ==1 ? 'Sammankopplad avvikelse' : 'Sammankopplade avvikelser'; ?></h3>
							</div>
							<div class="box-body">
								<ul class="list-unstyled">
								<?php foreach($getEventAnalysisDeviationMap as $mapKey): ?>
									<li><?php echo safe_anchor('deviation/view', $mapKey, $getDeviationNames[$mapKey]); ?></li>
								<?php endforeach; ?>
								</ul>
							</div>
						</div>
						<?php endif; ?>
						<?php if( ! empty($getEventAnalysisRiskAssessmentsMap) ): ?>
						<div class="box box-solid">
							<div class="box-header with-border">
								<h3 class="box-title"><?php echo count($getEventAnalysisRiskAssessmentsMap) ==1 ? 'Sammankopplad riskbedömning' : 'Sammankopplade riskbedömningar'; ?></h3>
							</div>
							<div class="box-body">
								<ul class="list-unstyled">
								<?php foreach($getEventAnalysisRiskAssessmentsMap as $mapKey): ?>
									<li><?php echo safe_anchor('riskassessments/view', $mapKey, $getRiskAnalysisNames[$mapKey]); ?></li>
								<?php endforeach; ?>
								</ul>
							</div>
						</div>
						<?php endif; ?>
						<?php if( empty($getEventAnalysisDeviationMap) && empty($getEventAnalysisRiskAssessmentsMap) ): ?>
						<div class="box box-solid no-print">
							<div class="box-header with-border">
								<h3 class="box-title">Avvikelser och riskbedömningar</h3>
							</div>
							<div class="box-body">
								Inga sammankopplade funna.
							</div>
						</div>
						<?php endif; ?>
						<?php echo safe_anchor('eventanalysis/connect', $getEventAnalysis->id, 'Anslut avvikelser och riskbedömningar', array('class' => 'btn btn-primary btn-block margin-bottom no-print')); ?>
					</div>
					<div class="col-md-8">
						<?php if(!empty($getEventAnalysisQuestions[0])) { ?>
							<div class="box">
								<div class="box-header with-border">
									<h3 class="box-title">
										Frågor och svar
									</h3>
								</div>
								<div class="box-body no-padding">
								<?php
									foreach($getEventAnalysisQuestions[0] as $question):
										echo '<div class="event">';
										foreach($eventFields as $key => $val):
											viewInputs($val + array('value' => $question->$key, 'prepend' => '<p>', 'append' => '</p>'));
										endforeach;
										if(isset($getEventAnalysisQuestions[$question->id])) {
											echo '<div class="events">';
												foreach($getEventAnalysisQuestions[$question->id] as $questionChild):
													echo '<div class="eventChild boxChild">';
													foreach($eventFields as $keyChild => $valChild):
														viewInputs($valChild + array('value' => $questionChild->$keyChild, 'prepend' => '<p>', 'append' => '</p>'));
													endforeach;
													echo '</div>';
												endforeach;
											echo '</div>';
										}
										echo '</div>';
									endforeach; ?>
								</div>
							</div>
						<?php } ?>
						<?php if(!empty($getEventAnalysisActionList)) { ?>
							<div class="box">
								<div class="box-header with-border">
									<h3 class="box-title">
										Åtgärdslista
									</h3>
								</div>
								<div class="box-body no-padding">
								<?php
									foreach($getEventAnalysisActionList as $action):
										echo '<div class="event">';
										foreach($actionListFields as $key => $val):
											viewInputs($val + array('value' => $action->$key, 'prepend' => '<p>', 'append' => '</p>'));
										endforeach;
										echo '</div>';
									endforeach; ?>
								</div>
							</div>
						<?php } ?>
					</div>
					<!-- /.col-md-9-->
				</div>
				<!-- /.row -->
			</section>
			<!-- /section -->
		</div>
  </div>
  <!-- /.content-wrapper -->
	<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title" id="myModalLabel"><?php echo lang('deviation_share_link'); ?></h4>
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				</div>
				<div class="modal-body">
					<?php
					// Ensure proper base URL with HTTP protocol
					$base_url = rtrim(config_item('base_url'), '/');
					if (empty($base_url) || !parse_url($base_url, PHP_URL_SCHEME)) {
						$base_url = 'http' . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 's' : '') . '://' . $_SERVER['HTTP_HOST'];
					}
					$share_url = $base_url . '/eventanalysisview/view/' . $ea_id;
					?>
					<a id="copy-text" href="<?php echo $share_url; ?>" target="_blank"><?php echo $share_url; ?></a>
					<button onClick="trackEventanalysisShare('<?php echo $ea_id; ?>')"
					class="btn btn-default" style="float: right">
						<i class="fa fa-copy" aria-hidden="true"></i>
					</button>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<script>
	function trackEventanalysisShare(eventanalysisId) {
		// Copy the link to clipboard first
		var copyText = document.getElementById('copy-text').innerText;
		navigator.clipboard.writeText(copyText).then(function() {
			console.log('Link copied to clipboard successfully');
		}).catch(function(err) {
			console.error('Failed to copy link: ', err);
		});
		
		// Track the share
		var shareUrl = copyText;

		$.ajax({
			url: '<?php echo site_url('eventanalysis/track_share'); ?>',
			type: 'POST',
			data: {
				eventanalysis_id: eventanalysisId,
				share_url: shareUrl,
				'<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
			},
			dataType: 'json',
			success: function(response) {
				console.log('Event analysis share tracked successfully:', response);
				// Show success message
				if (typeof toastr !== 'undefined') {
					toastr.success('Link copied and share tracked successfully!');
				} else {
					alert('Link copied and share tracked successfully!');
				}
			},
			error: function(xhr, status, error) {
				console.log('Failed to track event analysis share:', error);
				console.log('Response:', xhr.responseText);
				// Show error message
				if (typeof toastr !== 'undefined') {
					toastr.error('Failed to track share: ' + error);
				} else {
					alert('Failed to track share: ' + error);
				}
			}
		});
	}
	</script>

<?php $this->load->view('template/footer');