<?php $this->load->view('template/header-selectize-js'); ?>
<?php 
  $issueTitle = '';
  $issueType = '';
  $issueDescription = '';
	if ($page > 1) {
		$cur_fields = isset($deviation_page1_fields) ? $deviation_page1_fields : $deviation['fields'];
		$cur_options = isset($options_page1) ? $options_page1 : $options;
		foreach($cur_fields as $key => $val) {
			if ($val->input == 'input' && $val->required_kvalprak == 1) {
				$issueTitle = $val->answer;
			}
			if ($val->input == 'dropdown' && $val->required_kvalprak == 1) {
				$o = isset($cur_options[$val->df_id]) ? $cur_options[$val->df_id] : array();
				$issueType = $o[$val->answer]->name;
			}
			if ($val->input == 'text_wysiwyg' && $val->required_kvalprak == 1 && isset($val->answer)) {
				$issueDescription = trim(strip_tags($val->answer));
			}
		}
		if (isset($deviation_page2_fields)) {
			foreach($deviation_page2_fields as $key => $val) {
				if ($val->input == 'input' && $val->required_kvalprak == 1) {
					$issueTitle = $val->answer;
				}
				if ($val->input == 'dropdown' && $val->required_kvalprak == 1) {
					$o = isset($options_page1[$val->df_id]) ? $options_page1[$val->df_id] : array();
					$issueType = $o[$val->answer]->name;
				}
				if ($val->input == 'text_wysiwyg' && $val->required_kvalprak == 1) {
					$issueDescription = trim(strip_tags($val->answer));
				}
			}
		}
		$issueTitle = str_replace('"', '\'', $issueTitle);
		$issueType = str_replace('"', '\'', $issueType);
		$issueDescription = str_replace('"', '\'', $issueDescription);
		$issueDescription = str_replace('&nbsp;', ' ', $issueDescription);
		$issueDescription = str_replace('\r\n', ' ', $issueDescription);
		$issueDescription = str_replace('\n', ' ', $issueDescription);
		$issueDescription = str_replace('\r', ' ', $issueDescription);
	}
?>
<script type="text/javascript">
	function openAssistant(){
		const frame = document.getElementById("optobot")
    frame.setAttribute('src', `https://orna-analys.kvalprak.se/chatbot/<?php echo $this->config->item('program_name') ?>?solve=true&open=true&issueTitle=<?php echo $issueTitle ?>&issueType=<?php echo $issueType ?>&issueDescription=<?php echo $issueDescription ?>`);
	}
</script>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
	<div class="container">
			<?php
				echo form_open(NULL,array(
					'id' => 'form-company-group',
					'autocomplete' => 'off'
				));
				echo form_hidden('uuid_kvalprak',$uuid_kvalprak);
			?>
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php if ($page > 1 && in_array($this->auth_email, ['<EMAIL>', '<EMAIL>'])): ?>
				<a title="Redigera" href="#" onClick="openAssistant()" class="btn btn-primary mr-2">Föreslå lösning på avvikelsen</a>
				<?php endif; ?>
				<?php
				echo form_submit('form_next', lang('deviation_submit'), array('class' => 'btn btn-primary'));
				?>
				<?php if ($page > 1): ?>
					<a class="btn btn-default" title="<?php echo lang('deviation_share') ?>" data-toggle="modal" data-target="#myModal">
						<i class="fa fa-share-alt" aria-hidden="true" style="margin-right: 5px"></i>
					</a>
					<?php
						echo icon_anchor('deviation/attachments', $uuid_kvalprak ,'<i class="fa fa-paperclip" aria-hidden="true"></i>',
							array(
							'title' => 'Bilagor',
							'class' => 'btn btn-default'
							)
						);
					?>
					<a class="btn btn-default" title="Redigera" href="/deviation/edit/1/<?php echo $uuid_kvalprak;?>">
						<i class="fa fa-pencil" aria-hidden="true" style="margin-right: 5px"></i>
					</a>
				<?php endif; ?>
				<a href="javascript:goBack('/deviation');" title="<?php echo lang('cancel') ?>" class="btn btn-default"><i class="fa fa-reply" aria-hidden="true"></i></a>
			</div>
			<h1>Avvikelserapport
				<small>
				<?php
					$page_name = 'Steg ' . $page . ': ' . ($page == 1 ? lang('stage1_reported') : (
						$page == 2 ? lang('stage2_started') : lang('stage3_decision')
					));
					echo $page_name;
				?>
				</small>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box box-solid">
					<div>
						<?php if ($page > 1): ?>
							<div class="accordion-header">
							<h4>
								Steg 1 : <?php echo lang('stage1_reported');?> <small style="color: #00875A">completed</small>
								<a data-toggle="collapse" data-target="#stage1" class="float-right collapsed">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
									<i class="fa fa-chevron-up" aria-hidden="true"></i>
								</a>
							</h4>
							<div class="box-body no-padding collapse" id="stage1" aria-hidden="true">
								<?php 
									foreach($deviation_page1_fields as $key => $val):
										$s = isset($selected_page1[$val->df_id]) ? $selected_page1[$val->df_id] : $val->answer;
										if( ! empty($s) )
										{
											$o = isset($options_page1[$val->df_id]) ? $options_page1[$val->df_id] : array();
											forms_view($val->input, $val->df_id, $val->title, '', $o, $s, NULL, '');
										}
									endforeach;
								?>
							</div>
							</div>
						<?php endif; ?>
						<?php if ($page > 2): ?>
							<div class="accordion-header">
							<h4>
								Steg 2: <?php echo lang('stage2_started');?> <small style="color: #00875A">completed</small>
								<a data-toggle="collapse" data-target="#stage2" class="float-right collapsed">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
									<i class="fa fa-chevron-up" aria-hidden="true"></i>
								</a>
							</h4>
							<div class="box-body no-padding collapse" id="stage2" aria-hidden="true">
								<?php 
									foreach($deviation_page2_fields as $key => $val):
										$s = isset($selected_page2[$val->df_id]) ? $selected_page2[$val->df_id] : $val->answer;
										if( ! empty($s) )
										{
											$o = isset($options_page2[$val->df_id]) ? $options_page2[$val->df_id] : array();
											forms_view($val->input, $val->df_id, $val->title, '', $o, $s, NULL, '');
										}
									endforeach;
								?>
							</div>
							</div>
						<?php endif; ?>
						<div class="accordion-header">
						<h4>
							<?php echo $page_name; ?>
							<a data-toggle="collapse" data-target="#curStage" class="float-right">
								<i class="fa fa-chevron-down" aria-hidden="true"></i>
								<i class="fa fa-chevron-up" aria-hidden="true"></i>
							</a>
						</h4>

						<div class="box-body no-padding collapse in show" id="curStage" aria-hidden="true">
							<?php echo validation_errors();	?>
							<?php
							$odd_even = 'odd';
							foreach($fields['all'] as $key => $val):
								$odd_even = $odd_even === 'even' ? 'odd' : 'even';
								$required = ($val->required == 2 || $val->required_kvalprak) ? 1 : 0;
								$o = isset($options[$val->df_id]) ? $options[$val->df_id] : array();
								$s = isset($selected[$val->df_id]) ? $selected[$val->df_id] : NULL;
								forms_generator($val->input, $val->df_id, $val->title, $val->description, $o, $s, '{"required":'.$required.'}', $odd_even, TRUE, NULL, 'deviation_');
							endforeach;
							?>
						</div>
						</div>
						<!-- /.box-body -->
						<div class="box-footer clearfix">
						<?php
							// if( $page > 1 && $page <= 3 )
								// echo form_submit('form_previous', lang('previous_page'), array('class' => 'btn btn-sm btn-default btn-flat float-left'));
							if( $page >= 1 && $page <= 3 )
							echo form_submit('form_next', lang('save_and_next_stage'), array('class' => 'btn btn-sm btn-default btn-flat float-right'));
						?>
						</div>
					</div>
					</div>
					<!-- /. box -->
				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</section>
		<!-- /.content -->
		<?php echo form_close(); ?>
	</div>
	</div>
	<!-- /.content-wrapper -->
	<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title" id="myModalLabel"><?php echo lang('deviation_share_link'); ?></h4>
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				</div>
				<div class="modal-body">
					<a id="copy-text" href="https://<?php echo $_SERVER['SERVER_NAME'] ?>/viewdeviation/<?php echo $uuid_kvalprak; ?>" target="_blank">https://<?php echo $_SERVER['SERVER_NAME'] ?>/viewdeviation/<?php echo $uuid_kvalprak; ?></a>
					<button onClick="navigator.clipboard.writeText(document.getElementById('copy-text').innerText)"
					class="btn btn-default" style="float: right">
						<i class="fa fa-copy" aria-hidden="true"></i>
					</button>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>
<?php $this->load->view('template/footer-deviation'); ?>