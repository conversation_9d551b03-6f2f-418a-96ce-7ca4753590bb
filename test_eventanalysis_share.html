<!DOCTYPE html>
<html>
<head>
    <title>Test Event Analysis Share</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Event Analysis Share</h1>
    
    <button onclick="testEventAnalysisShare()">Test Share Event Analysis</button>
    <div id="result"></div>
    
    <script>
    function testEventAnalysisShare() {
        var eventanalysisId = 'ddc3481f-51a2-42bb-9da7-7a7bc301db39'; // Replace with actual ID
        var shareUrl = 'http://localhost/eventanalysisview/view/' + eventanalysisId;
        
        console.log('Testing with ID:', eventanalysisId);
        
        $.ajax({
            url: 'http://localhost/eventanalysis/track_share',
            type: 'POST',
            data: {
                eventanalysis_id: eventanalysisId,
                share_url: shareUrl,
                'csrf_test_name': 'test_token' // You might need actual CSRF token
            },
            dataType: 'json',
            success: function(response) {
                console.log('Success:', response);
                document.getElementById('result').innerHTML = '<div style="color: green;">Success: ' + JSON.stringify(response) + '</div>';
            },
            error: function(xhr, status, error) {
                console.log('Error:', error);
                console.log('Response:', xhr.responseText);
                document.getElementById('result').innerHTML = '<div style="color: red;">Error: ' + xhr.responseText + '</div>';
            }
        });
    }
    </script>
</body>
</html>
